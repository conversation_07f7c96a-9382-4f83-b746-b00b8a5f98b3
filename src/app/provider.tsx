'use client';

import { Provider } from 'react-redux';
import { ReactNode, useEffect } from 'react';
import { store } from '@/store';
import { setAuthorizationToRequest } from '../utils/auth';
import { getNetworks, getAssets } from '@/store/metadata.store';
import { getUserProfile } from '../store/user.store';

export const AppProvider = ({
  children,
  authorization,
}: {
  children: ReactNode;
  authorization?: string;
}) => {
  const initializeMetadata = () => {
    store.dispatch(getNetworks());
    store.dispatch(getAssets());
  };

  useEffect(() => {
    initializeMetadata();
  }, []);

  useEffect(() => {
    if (authorization) {
      setAuthorizationToRequest(authorization);
      store.dispatch(getUserProfile());
      return;
    }
  }, [authorization]);

  return <Provider store={store}>{children}</Provider>;
};
