'use client';

import { Provider } from 'react-redux';
import { ReactNode, useEffect } from 'react';
import { store } from '@/store';
import { setAuthorizationToRequest } from '../utils/auth';
import { getNetworks, getAssets } from '@/store/metadata.store';
import { getUserProfile, logoutUser } from '../store/user.store';
import { AppBroadcast, BROADCAST_EVENTS } from '@/libs/broadcast';

export const AppProvider = ({
  children,
  authorization,
}: {
  children: ReactNode;
  authorization?: string;
}) => {
  const initializeMetadata = () => {
    store.dispatch(getNetworks());
    store.dispatch(getAssets());
  };

  useEffect(() => {
    initializeMetadata();
  }, []);

  useEffect(() => {
    if (authorization) {
      setAuthorizationToRequest(authorization);
      // Dispatch getUserProfile and handle potential failures
      store.dispatch(getUserProfile()).catch((error) => {
        console.log('Initial profile fetch failed:', error);
        // The error handling is already done in the getUserProfile thunk
      });
      return;
    }
  }, [authorization]);

  useEffect(() => {
    // Listen for logout broadcast events
    const handleLogout = () => {
      console.log('Logout broadcast event received');
      store.dispatch(logoutUser());
    };

    AppBroadcast.on(BROADCAST_EVENTS.LOGOUT, handleLogout);

    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.LOGOUT, handleLogout);
    };
  }, []);

  return <Provider store={store}>{children}</Provider>;
};
