import { Outfit } from 'next/font/google';
import './globals.css';

import { SidebarProvider } from '@/context/SidebarContext';
import { ThemeProvider } from '@/context/ThemeContext';
import { AppProvider } from './provider';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
const outfit = Outfit({
  subsets: ['latin'],
});
import { cookies } from 'next/headers';
import { COOKIES_ACCESS_TOKEN_KEY } from '@/constants';

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const accessToken = (await cookies()).get(COOKIES_ACCESS_TOKEN_KEY)?.value || '';
  console.log(accessToken, 'accessToken');

  return (
    <html lang="en">
      <body className={`${outfit.className} dark:bg-gray-900`}>
        <AppProvider authorization={accessToken}>
          <ToastContainer
            autoClose={2000}
            position="top-right"
            icon={false}
            pauseOnHover
            closeButton={false}
            hideProgressBar
            toastStyle={{
              position: 'relative',
              overflow: 'visible',
            }}
          />
          <ThemeProvider>
            <SidebarProvider>{children}</SidebarProvider>
          </ThemeProvider>
        </AppProvider>
      </body>
    </html>
  );
}
