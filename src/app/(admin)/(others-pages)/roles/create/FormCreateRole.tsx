'use client';

import React, { useState, useEffect } from 'react';
import Label from '@/components/form/Label';
import Input from '@/components/form/input/InputField';
import Button from '@/components/ui/button/Button';
import { useForm } from 'react-hook-form';
import Checkbox from '@/components/form/input/Checkbox';
import rf from '@/services/RequestFactory';
import { toastError, toastSuccess } from '@/libs/toast';
import { TPermission } from '@/types/permission';
import { PermissionEnum } from '@/types/enums';

// Maximum number of permissions to show before "Show less" button
const PERMISSIONS_THRESHOLD = 8;

// Transform API permissions to form structure
const transformPermissionsToFormStructure = (permissions: TPermission[]) => {
  const resourcePermissions: Record<string, string[]> = {};

  permissions.forEach((permission) => {
    if (!resourcePermissions[permission.resource]) {
      resourcePermissions[permission.resource] = [];
    }
    resourcePermissions[permission.resource] = [
      ...resourcePermissions[permission.resource],
      ...permission.action,
    ];
  });

  return resourcePermissions;
};

// Transform form data to API format
const transformFormDataToApiFormat = (checked: Record<string, Record<string, boolean>>) => {
  const permissions: TPermission[] = [];

  Object.keys(checked).forEach((resource) => {
    const actions = Object.keys(checked[resource]).filter((action) => checked[resource][action]);

    if (actions.length > 0) {
      permissions.push({
        resource: resource as PermissionEnum,
        action: actions,
      });
    }
  });

  return permissions;
};

const FormCreateRole = () => {
  const [resourcePermissions, setResourcePermissions] = useState<Record<string, string[]>>({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [checked, setChecked] = useState<Record<string, Record<string, boolean>>>({});

  const [collapsedPermissions, setCollapsedPermissions] = useState<Record<string, boolean>>({});

  const resources = Object.keys(resourcePermissions);

  // Load permissions data on component mount
  useEffect(() => {
    const loadPermissions = async () => {
      try {
        setLoading(true);
        const permissions = await rf.getRequest('PermissionRequest').getPermissions();
        const transformedPermissions = transformPermissionsToFormStructure(permissions);
        setResourcePermissions(transformedPermissions);

        // Initialize checked state
        const initialChecked: Record<string, Record<string, boolean>> = {};
        Object.keys(transformedPermissions).forEach((resource) => {
          initialChecked[resource] = {};
          transformedPermissions[resource].forEach((action) => {
            initialChecked[resource][action] = false;
          });
        });
        setChecked(initialChecked);
      } catch (error: any) {
        toastError('Error', error?.message || 'Failed to load permissions');
        console.error('Load permissions error:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPermissions();
  }, []);

  const handleToggleAll = (res: string, e: React.MouseEvent) => {
    e.stopPropagation();

    const allChecked = Object.values(checked[res]).every(Boolean);
    setChecked((prev: any) => ({
      ...prev,
      [res]: resourcePermissions[res as keyof typeof resourcePermissions].reduce(
        (acc: any, perm: string) => {
          acc[perm] = !allChecked;
          return acc;
        },
        {}
      ),
    }));
  };

  const handleTogglePermission = (res: string, perm: string) => {
    setChecked((prev: any) => ({
      ...prev,
      [res]: {
        ...prev[res],
        [perm]: !prev[res][perm],
      },
    }));
  };

  const toggleShowMore = (res: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setCollapsedPermissions((prev) => ({
      ...prev,
      [res]: !prev[res],
    }));
  };

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();

  const onSubmit = async (data: any) => {
    try {
      setSubmitting(true);

      // Step 1: Create the role
      const roleResponse = await rf.getRequest('RoleRequest').createRole({
        name: data.name,
        description: data.description || '',
      });

      // Step 2: Create permissions for the role
      const selectedPermissions = transformFormDataToApiFormat(checked);

      if (selectedPermissions.length > 0) {
        await rf.getRequest('PermissionRequest').createPermission({
          role_id: roleResponse.id,
          permissions: selectedPermissions,
        });
      }

      toastSuccess('Success', 'Role created successfully!');

      // Reset form
      reset();
      setChecked((prev) => {
        const resetChecked: Record<string, Record<string, boolean>> = {};
        Object.keys(prev).forEach((resource) => {
          resetChecked[resource] = {};
          Object.keys(prev[resource]).forEach((action) => {
            resetChecked[resource][action] = false;
          });
        });
        return resetChecked;
      });
    } catch (error: any) {
      toastError('Error', error?.message || 'Failed to create role');
      console.error('Create role error:', error);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="border-brand-500 mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
            <p className="text-gray-500 dark:text-gray-400">Loading permissions...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
      <form className="flex flex-col" onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
          <div className="col-span-2 lg:col-span-1">
            <Label>
              Name <span className="text-red-400">*</span>
            </Label>
            <Input
              {...register('name', { required: true })}
              type="text"
              placeholder="Enter your name"
            />
            {errors.name && <p className="text-xs text-red-400">This field is required</p>}
          </div>
          <div className="col-span-2 lg:col-span-1">
            <Label>Description</Label>
            <textarea
              {...register('description')}
              placeholder={'Enter your description'}
              rows={3}
              className={
                'shadow-theme-xs focus:outline-hidden focus:border-brand-300 focus:ring-3 focus:ring-brand-500/10 dark:focus:border-brand-800 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90'
              }
            />
          </div>
        </div>

        <div className="mt-8">
          <h3 className="mb-4 text-base font-medium text-gray-800 dark:text-white/90">
            Permissions
          </h3>
          <div className="flex w-full flex-col space-y-3">
            {resources.map((res) => {
              const permissions = resourcePermissions[res as keyof typeof resourcePermissions];
              const permCount = permissions.length;
              const showToggleButton = permCount > PERMISSIONS_THRESHOLD;
              const isCollapsed = collapsedPermissions[res];
              const displayedPermissions =
                showToggleButton && isCollapsed
                  ? permissions.slice(0, PERMISSIONS_THRESHOLD)
                  : permissions;

              return (
                <div
                  key={res}
                  className="rounded-lg border border-gray-100 dark:border-white/[0.05]"
                >
                  <div className="grid grid-cols-1 gap-4 p-4 md:grid-cols-12">
                    {/* Left column - Resource name */}
                    <div className="flex items-center gap-2 md:col-span-3">
                      <div onClick={(e) => handleToggleAll(res, e)}>
                        <Checkbox
                          checked={Object.values(checked[res]).every(Boolean)}
                          onChange={() => {}}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {res}
                      </span>
                      <span className="ml-1 text-xs text-gray-500 dark:text-gray-400">
                        ({permCount})
                      </span>
                    </div>

                    {/* Right column - Permissions */}
                    <div className="md:col-span-9">
                      <div className="flex flex-wrap gap-4">
                        {displayedPermissions.map((perm) => (
                          <div
                            key={perm}
                            className="flex cursor-pointer items-center gap-2"
                            onClick={() => handleTogglePermission(res, perm)}
                          >
                            <Checkbox checked={checked[res][perm]} onChange={() => {}} />
                            <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              {perm}
                            </span>
                          </div>
                        ))}

                        {showToggleButton && (
                          <button
                            type="button"
                            className="text-brand-500 hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300 text-xs font-medium"
                            onClick={(e) => toggleShowMore(res, e)}
                          >
                            {isCollapsed
                              ? `Show ${permCount - PERMISSIONS_THRESHOLD} more`
                              : 'Show less'}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="mt-6 flex items-center gap-3 px-2 lg:justify-end">
          <Button size="sm" variant="outline" disabled={submitting}>
            Cancel
          </Button>
          <Button size="sm" type="submit" disabled={submitting}>
            {submitting ? 'Creating Role...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default FormCreateRole;
