'use client';

import React, { useState, useEffect } from 'react';
import Button from '@/components/ui/button/Button';
import Checkbox from '@/components/form/input/Checkbox';
import rf from '@/services/RequestFactory';
import { toastError, toastSuccess } from '@/libs/toast';
import { TPermission } from '@/types/permission';
import { TRole } from '@/types/role';
import { useRouter } from 'next/navigation';
import { PermissionEnum } from '@/types/enums';

// Maximum number of permissions to show before "Show less" button
const PERMISSIONS_THRESHOLD = 8;

// Transform API permissions to form structure (same as role creation)
const transformPermissionsToFormStructure = (permissions: TPermission[]) => {
  const resourcePermissions: Record<string, string[]> = {};

  permissions.forEach((permission) => {
    if (!resourcePermissions[permission.resource]) {
      resourcePermissions[permission.resource] = [];
    }
    resourcePermissions[permission.resource] = [
      ...resourcePermissions[permission.resource],
      ...permission.action,
    ];
  });

  return resourcePermissions;
};

// Map role permissions to checked state
const mapRolePermissionsToCheckedState = (
  allPermissions: Record<string, string[]>,
  rolePermissions: TPermission[]
) => {
  const checkedState: Record<string, Record<string, boolean>> = {};

  // Initialize all permissions as unchecked
  Object.keys(allPermissions).forEach((resource) => {
    checkedState[resource] = {};
    allPermissions[resource].forEach((action) => {
      checkedState[resource][action] = false;
    });
  });

  // Mark role permissions as checked
  rolePermissions.forEach((permission) => {
    if (checkedState[permission.resource]) {
      permission.action.forEach((action) => {
        if (checkedState[permission.resource][action] !== undefined) {
          checkedState[permission.resource][action] = true;
        }
      });
    }
  });

  return checkedState;
};

// Transform form data to API format
const transformFormDataToApiFormat = (checked: Record<string, Record<string, boolean>>) => {
  const permissions: TPermission[] = [];

  Object.keys(checked).forEach((resource) => {
    const actions = Object.keys(checked[resource]).filter((action) => checked[resource][action]);

    if (actions.length > 0) {
      permissions.push({
        resource: resource as PermissionEnum,
        action: actions,
      });
    }
  });

  return permissions;
};

interface RoleDetailProps {
  roleId: string;
}

const RoleDetail = ({ roleId }: RoleDetailProps) => {
  const router = useRouter();
  const [role, setRole] = useState<TRole | null>(null);
  const [resourcePermissions, setResourcePermissions] = useState<Record<string, string[]>>({});
  const [checked, setChecked] = useState<Record<string, Record<string, boolean>>>({});
  const [loading, setLoading] = useState(true);
  const [collapsedPermissions, setCollapsedPermissions] = useState<Record<string, boolean>>({});

  // Edit mode state - enabled by default for better UX
  const [isEditMode, setIsEditMode] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [originalChecked, setOriginalChecked] = useState<Record<string, Record<string, boolean>>>(
    {}
  );

  const resources = Object.keys(resourcePermissions);

  // Load role data and permissions on component mount
  useEffect(() => {
    const loadRoleData = async () => {
      try {
        setLoading(true);

        // Fetch all available permissions and role details in parallel
        const [allPermissions, roleResponse] = await Promise.all([
          rf.getRequest('PermissionRequest').getPermissions(),
          rf.getRequest('PermissionRequest').getRolePermission(roleId),
        ]);

        // Set role information
        setRole({
          id: roleResponse.role_id,
          name: roleResponse.role_name,
          description: roleResponse.role_description,
        });

        // Transform all available permissions to form structure
        const transformedPermissions = transformPermissionsToFormStructure(allPermissions);
        setResourcePermissions(transformedPermissions);

        // Map role permissions to checked state
        const checkedState = mapRolePermissionsToCheckedState(
          transformedPermissions,
          roleResponse.permissions
        );

        setChecked(checkedState);
        setOriginalChecked(checkedState);
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to load role details';
        toastError('Error', errorMessage);
        console.error('Load role details error:', error);
      } finally {
        setLoading(false);
      }
    };

    loadRoleData();
  }, [roleId]);

  const toggleShowMore = (res: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setCollapsedPermissions((prev) => ({
      ...prev,
      [res]: !prev[res],
    }));
  };

  const handleBackToRoles = () => {
    router.push('/roles');
  };

  const handleEditToggle = () => {
    if (isEditMode) {
      // Cancel editing - revert to original state
      setChecked(originalChecked);
    }
    setIsEditMode(!isEditMode);
  };

  const handleToggleAll = (res: string, e: React.MouseEvent) => {
    if (!isEditMode) return;
    e.stopPropagation();

    const allChecked = Object.values(checked[res] || {}).every(Boolean);
    setChecked((prev) => ({
      ...prev,
      [res]: resourcePermissions[res].reduce((acc: Record<string, boolean>, perm: string) => {
        acc[perm] = !allChecked;
        return acc;
      }, {}),
    }));
  };

  const handleTogglePermission = (res: string, perm: string) => {
    if (!isEditMode) return;
    setChecked((prev) => ({
      ...prev,
      [res]: {
        ...prev[res],
        [perm]: !prev[res][perm],
      },
    }));
  };

  const onSubmit = async () => {
    if (!role) return;

    try {
      setSubmitting(true);

      // Update permissions
      const selectedPermissions = transformFormDataToApiFormat(checked);

      await rf.getRequest('PermissionRequest').updateRolePermissions({
        permissions: selectedPermissions,
        role_id: parseInt(roleId),
      });

      // Update original state to new state
      setOriginalChecked(checked);

      toastSuccess('Success', 'Role permissions updated successfully!');
      setIsEditMode(false);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to update role permissions';
      toastError('Error', errorMessage);
      console.error('Update role permissions error:', error);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="border-brand-500 mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
            <p className="text-gray-500 dark:text-gray-400">Loading role details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!role) {
    return (
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <div className="py-12 text-center">
          <p className="mb-4 text-gray-500 dark:text-gray-400">Role not found</p>
          <Button size="sm" variant="outline" onClick={handleBackToRoles}>
            Back to Roles
          </Button>
        </div>
      </div>
    );
  }

  const handleSaveChanges = () => {
    onSubmit();
  };

  return (
    <div className="space-y-6">
      {/* Role Information Card */}
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <div className="mb-6 flex items-start justify-between">
          <div className="mr-6 flex-1">
            <div>
              <h1 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
                {role.name}
              </h1>
              <p className="mb-4 text-gray-600 dark:text-gray-400">
                {role.description || 'No description provided'}
              </p>
            </div>

            <div className="mt-4 grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Role ID:</span>
                <span className="ml-2 font-medium text-gray-800 dark:text-white/90">{role.id}</span>
              </div>
            </div>
          </div>
          <div className="flex gap-3">
            <Button size="sm" variant="outline" onClick={handleBackToRoles} type="button">
              Back to Roles
            </Button>
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={handleEditToggle}
                disabled={submitting}
                type="button"
              >
                {isEditMode ? 'Cancel' : 'Edit Role'}
              </Button>
              {isEditMode && (
                <Button
                  size="sm"
                  variant="primary"
                  onClick={handleSaveChanges}
                  disabled={submitting}
                  type="button"
                >
                  {submitting ? 'Saving...' : 'Save Changes'}
                </Button>
              )}
            </>
          </div>
        </div>
      </div>

      {/* Permissions Card */}
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <h3 className="mb-4 text-base font-medium text-gray-800 dark:text-white/90">
          Role Permissions
        </h3>

        {resources.length === 0 ? (
          <p className="py-8 text-center text-gray-500 dark:text-gray-400">
            No permissions assigned to this role
          </p>
        ) : (
          <div className="flex w-full flex-col space-y-3">
            {resources.map((res) => {
              const permissions = resourcePermissions[res];
              const permCount = permissions.length;
              const showToggleButton = permCount > PERMISSIONS_THRESHOLD;
              const isCollapsed = collapsedPermissions[res];
              const displayedPermissions =
                showToggleButton && isCollapsed
                  ? permissions.slice(0, PERMISSIONS_THRESHOLD)
                  : permissions;

              return (
                <div
                  key={res}
                  className="rounded-lg border border-gray-100 dark:border-white/[0.05]"
                >
                  <div className="grid grid-cols-1 gap-4 p-4 md:grid-cols-12">
                    {/* Left column - Resource name */}
                    <div className="flex items-center gap-2 md:col-span-3">
                      <div onClick={(e) => handleToggleAll(res, e)}>
                        <Checkbox
                          checked={Object.values(checked[res] || {}).every(Boolean)}
                          onChange={() => {}}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {res}
                      </span>
                      <span className="ml-1 text-xs text-gray-500 dark:text-gray-400">
                        ({permCount})
                      </span>
                    </div>

                    {/* Right column - Permissions */}
                    <div className="md:col-span-9">
                      <div className="flex flex-wrap gap-4">
                        {displayedPermissions.map((perm) => (
                          <div
                            key={perm}
                            className="flex cursor-pointer items-center gap-2"
                            onClick={() => handleTogglePermission(res, perm)}
                          >
                            <Checkbox checked={checked[res]?.[perm] || false} onChange={() => {}} />
                            <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              {perm}
                            </span>
                          </div>
                        ))}

                        {showToggleButton && (
                          <button
                            type="button"
                            className="text-brand-500 hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300 text-xs font-medium"
                            onClick={(e) => toggleShowMore(res, e)}
                          >
                            {isCollapsed
                              ? `Show ${permCount - PERMISSIONS_THRESHOLD} more`
                              : 'Show less'}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default RoleDetail;
