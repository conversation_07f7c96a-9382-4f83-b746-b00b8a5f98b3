import PageBreadcrumb from '@/components/common/PageBreadCrumb';
import React from 'react';
import { Metadata } from 'next';
import RoleDetail from './RoleDetail';

export const metadata: Metadata = {
  title: 'Settings - Role Details | VDAX Admin',
  description: 'This is Settings Role Details page for VDAX Admin',
};

interface RoleDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

const page = async ({ params }: RoleDetailPageProps) => {
  const { id } = await params;
  return (
    <div>
      <PageBreadcrumb pageTitle="Role Details" />
      <RoleDetail roleId={id} />
    </div>
  );
};

export default page;
