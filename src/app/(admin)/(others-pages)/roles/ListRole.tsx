'use client';

import React, { useMemo, useRef, useState } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import { TrashBinIcon, EyeIcon } from '@/icons';
import Button from '@/components/ui/button/Button';
import Link from 'next/link';
import rf from '@/services/RequestFactory';
import { formatUnixTimestamp } from '@/utils/format';
import { TRole } from '@/types/role';
import { useWindowSize } from '@/hooks/useWindowSize';
import { useModal } from '@/hooks/useModal';
import { toastError, toastSuccess } from '@/libs/toast';
import ConfirmModal from '@/components/ui/modal/ConfirmModal';

const ListRole = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();
  const { isOpen, openModal, closeModal } = useModal();
  const [roleToDelete, setRoleToDelete] = useState<TRole | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('RoleRequest').getRoles(params);
      return { data: res };
    } catch (e: any) {
      console.error('Get Roles Error', e?.message);
      return { data: [], cursor: null };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 320;
  }, [windowHeight]);

  const handleDeleteClick = (role: TRole) => {
    setRoleToDelete(role);
    openModal();
  };

  const handleDeleteConfirm = async () => {
    if (!roleToDelete) return;

    try {
      setIsDeleting(true);
      await rf.getRequest('RoleRequest').deleteRole(roleToDelete.id);

      // Refresh the data table
      if (dataTableRef.current) {
        (dataTableRef.current as any)?.refresh();
      }

      toastSuccess('Success', `Role "${roleToDelete.name}" has been deleted successfully!`);
      closeModal();
      setRoleToDelete(null);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete role';
      toastError('Error', errorMessage);
      console.error('Delete role error:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    closeModal();
    setRoleToDelete(null);
  };

  return (
    <>
      <div className="mb-8 flex justify-end">
        <Link href="/roles/create">
          <Button size="sm" variant="primary">
            Add New Role
          </Button>
        </Link>
      </div>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1102}
          ref={dataTableRef}
          getData={getData as any}
          height={tableHeight}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                {['ID', 'Created At', 'Name', 'Description', 'Action'].map((label, i) => (
                  <div
                    key={i}
                    className="text-theme-xs w-[25%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                  >
                    {label}
                  </div>
                ))}
              </div>
            );
          }}
          renderRow={(role: TRole) => {
            return (
              <div
                key={role.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[25%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {role.id}
                </div>
                <div className="text-theme-sm w-[25%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {role.created_at && formatUnixTimestamp(role.created_at)}
                </div>
                <div className="text-theme-sm w-[25%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  <Link
                    href={`/roles/${role.id}`}
                    className="hover:text-brand-500 dark:hover:text-brand-400 cursor-pointer transition-colors"
                  >
                    {role.name}
                  </Link>
                </div>
                <div className="text-theme-sm w-[25%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {role.description}
                </div>
                <div className="text-theme-sm flex w-[25%] items-center gap-2 px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  <Link
                    href={`/roles/${role.id}`}
                    className="hover:text-brand-500 dark:hover:text-brand-400 cursor-pointer transition-colors"
                    title="View/Edit Role Details"
                  >
                    <EyeIcon />
                  </Link>
                  <div
                    className="cursor-pointer transition-colors hover:text-red-500 dark:hover:text-red-400"
                    title="Delete Role"
                    onClick={() => handleDeleteClick(role)}
                  >
                    <TrashBinIcon />
                  </div>
                </div>
              </div>
            );
          }}
        />
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Role"
        message={`Are you sure you want to delete the role "${roleToDelete?.name}"?`}
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        variant="danger"
      />
    </>
  );
};

export default ListRole;
