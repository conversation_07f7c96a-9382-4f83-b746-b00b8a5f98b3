'use client';
import React, { useMemo, useRef } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import { TrashBinIcon } from '@/icons';
import Badge from '@/components/ui/badge/Badge';
import ButtonAddAdmin from './ButtonAddAdmin';
import rf from '@/services/RequestFactory';
import { TAdmin } from '@/types/admin';
import { formatUnixTimestamp } from '@/utils/format';
import { useWindowSize } from '@/hooks';
import ButtonEditAdmin from './ButtonEdtiAdmint';

const ListAdmin = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const onFetchData = () => {
    if (dataTableRef.current) {
      // Force refresh the data table
      (dataTableRef.current as any)?.refresh();
    }
  };

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('AdminRequest').listAdmin(params);
      return { data: res?.docs, cursor: res?.cursor };
    } catch (e: any) {
      console.error('Get List Admin Error', e?.message);
      return { data: [], cursor: null };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 320;
  }, [windowHeight]);

  return (
    <>
      <div className="mb-8 flex justify-end">
        <ButtonAddAdmin onFetchData={onFetchData} />
      </div>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1102}
          ref={dataTableRef}
          height={tableHeight}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[12%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Created At
                </div>
                <div className="text-theme-xs w-[16%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Name
                </div>
                <div className="text-theme-xs w-[22%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Email
                </div>
                <div className="text-theme-xs w-[12%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Last Login At
                </div>
                <div className="text-theme-xs w-[14%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Role
                </div>
                <div className="text-theme-xs w-[12%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Status
                </div>
                <div className="text-theme-xs w-[12%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Action
                </div>
              </div>
            );
          }}
          renderRow={(admin: TAdmin) => {
            return (
              <div
                key={admin.admin_id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[12%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(admin.created_at) || '--'}
                </div>
                <div className="text-theme-sm w-[16%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {admin.name || '--'}
                </div>
                <div className="text-theme-sm w-[22%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {admin.email || '--'}
                </div>
                <div className="text-theme-sm w-[12%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(admin.last_login_at) || '--'}
                </div>
                <div className="text-theme-sm flex w-[14%] items-center px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {admin.roles[0] || '--'}
                </div>
                <div className="text-theme-sm w-[12%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  <Badge size="sm" color={admin.status === 'ACTIVE' ? 'success' : 'error'}>
                    {admin.status?.toLowerCase()}
                  </Badge>
                </div>

                <div className="text-theme-sm flex w-[12%] items-center gap-2 px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  <ButtonEditAdmin onFetchData={onFetchData} admin={admin} />
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default ListAdmin;
