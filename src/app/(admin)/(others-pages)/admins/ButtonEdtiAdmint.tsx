'use client';

import React from 'react';
import { useModal } from '@/hooks/useModal';
import { EditIcon } from '@/icons';
import ModalEditAdmin from '@/modals/ModalEditAdmin';
import { TAdmin } from '@/types/admin';

const ButtonEditAdmin = ({ onFetchData, admin }: { onFetchData: () => void; admin: TAdmin }) => {
  const { isOpen, openModal, closeModal } = useModal();

  return (
    <>
      <div
        onClick={openModal}
        className="cursor-pointer hover:text-gray-800 dark:hover:text-gray-200"
      >
        <EditIcon />
      </div>

      {isOpen && (
        <ModalEditAdmin
          admin={admin}
          isOpen={isOpen}
          onClose={closeModal}
          onFetchData={onFetchData}
        />
      )}
    </>
  );
};

export default ButtonEditAdmin;
