'use client';

import React from 'react';
import { useModal } from '@/hooks/useModal';
import ModalAddAdmin from '@/modals/ModalAddAdmin';
import Button from '@/components/ui/button/Button';

const ButtonAddAdmin = ({ onFetchData }: { onFetchData: () => void }) => {
  const { isOpen, openModal, closeModal } = useModal();

  return (
    <>
      <Button onClick={openModal} size="sm" variant="primary">
        Add New Admin
      </Button>
      {isOpen && <ModalAddAdmin isOpen={isOpen} onClose={closeModal} onFetchData={onFetchData} />}
    </>
  );
};

export default ButtonAddAdmin;
