'use client';

import { useState } from 'react';
import TabBar from '@/components/ui/tab';
import TableKYCVerificationAll from './TableKYCVerificationAll';
import TableKYCVerificationPending from './TableKYCVerificationPending';
import TableKYCVerificationApproved from './TableKYCVerificationApproved';
import TableKYCVerificationRejected from './TableKYCVerificationRejected';

const tabs = [
  {
    label: 'All',
    value: 'all',
  },
  {
    label: 'Pending',
    value: 'pending',
  },
  {
    label: 'Approved',
    value: 'approved',
  },
  {
    label: 'Rejected',
    value: 'rejected',
  },
];

const TableKYCVerification = () => {
  const [activeTab, setActiveTab] = useState<string>('all');
  return (
    <div className="rounded-2xl bg-white dark:bg-white/[0.03] ">
      <div className="space-y-6">
        <div>
          <TabBar tabs={tabs} activeTab={activeTab} setActiveTab={setActiveTab} />

          <div className="rounded-b-xl border border-t-0 border-gray-200 p-6 pt-4 dark:border-gray-800">
            {activeTab === 'all' && <TableKYCVerificationAll />}
            {activeTab === 'pending' && <TableKYCVerificationPending />}
            {activeTab === 'approved' && <TableKYCVerificationApproved />}
            {activeTab === 'rejected' && <TableKYCVerificationRejected />}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TableKYCVerification;
