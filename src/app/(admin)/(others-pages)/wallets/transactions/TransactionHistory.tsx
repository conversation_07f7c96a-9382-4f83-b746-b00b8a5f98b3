'use client';

import { useState } from 'react';
import TabBar from '@/components/ui/tab';
import TableWalletDeposit from './TableWalletDeposit';
import TableWalletWithdraw from './TableWalletWithdraw';
import TableWalletTxCollect from './TableWalletTxCollect';
import TableWalletTxSeed from './TableWalletTxSeed';

const tabs = [
  {
    label: 'Deposit',
    value: 'Deposit',
  },
  {
    label: 'Withdraw',
    value: 'Withdraw',
  },
  {
    label: 'Collect',
    value: 'Collect',
  },
  {
    label: 'Seed',
    value: 'Seed',
  },
];

const TransactionHistory = () => {
  const [activeTab, setActiveTab] = useState<string>('Deposit');
  return (
    <div className="rounded-2xl bg-white dark:bg-white/[0.03] ">
      <div className="space-y-6">
        <div>
          <TabBar tabs={tabs} activeTab={activeTab} setActiveTab={setActiveTab} />

          <div className="rounded-b-xl border border-t-0 border-gray-200 p-6 pt-4 dark:border-gray-800">
            {activeTab === 'Deposit' && <TableWalletDeposit />}
            {activeTab === 'Withdraw' && <TableWalletWithdraw />}
            {activeTab === 'Collect' && <TableWalletTxCollect />}
            {activeTab === 'Seed' && <TableWalletTxSeed />}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionHistory;
