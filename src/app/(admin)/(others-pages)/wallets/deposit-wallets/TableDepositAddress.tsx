'use client';
import React, { useMemo, useRef, useState } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import { formatShortAddress } from '@/utils/format';
import { CopyIcon } from '@/icons';
import { copyToClipboard } from '@/utils/helper';
import rf from '@/services/RequestFactory';
import { useWindowSize } from '@/hooks/useWindowSize';
import SearchInput from '@/components/ui/search/index';
import { filterParams } from '@/utils/helper';
import { formatUnixTimestamp } from '@/utils/format';

const TableDepositAddress = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const [address, setAddress] = useState<string>('');
  const { windowHeight } = useWindowSize();

  const getData = async (params: any) => {
    try {
      const res = await rf
        .getRequest('WalletRequest')
        .getDepositAddresses(filterParams({ ...params, address }));
      return { data: res, cursor: null };
    } catch (e: any) {
      console.error('Get Deposit Address Error', e?.message);
      return { data: [], cursor: null };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 350;
  }, [windowHeight]);

  return (
    <>
      <div className="mb-8 flex items-end justify-between gap-x-6">
        <SearchInput setSearch={setAddress} placeholder="Search by address" />
      </div>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1102}
          ref={dataTableRef}
          height={tableHeight}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Wallet ID
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Created At
                </div>
                <div className="text-theme-xs w-[30%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Wallet
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Currency
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Type
                </div>
              </div>
            );
          }}
          renderRow={(wallet: any) => {
            return (
              <div
                key={wallet.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm flex w-[10%] items-center gap-2 px-4 py-3 text-gray-500 dark:text-gray-400">
                  {wallet.walletId}
                </div>
                <div className="text-theme-sm flex w-[20%] items-center gap-2 px-4 py-3 text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(wallet.createdAt)}
                </div>
                <div className="text-theme-sm flex w-[30%] items-center gap-2 px-4 py-3 text-gray-500 dark:text-gray-400">
                  {formatShortAddress(wallet.address)}
                  <CopyIcon
                    onClick={() => copyToClipboard(wallet.address)}
                    className="cursor-pointer"
                  />
                </div>

                <div className="text-theme-sm w-[20%] items-center px-4 py-3 text-start uppercase text-gray-500 dark:text-gray-400">
                  {wallet.currency}
                </div>
                <div className="text-theme-sm w-[20%] items-center px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {wallet.isExternal ? 'External' : 'Internal'}
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default TableDepositAddress;
