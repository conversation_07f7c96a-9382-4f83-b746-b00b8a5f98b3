'use client';

import { AppDataTable } from '@/components/tables/AppDataTable';
import { useMemo, useRef } from 'react';
import rf from '@/services/RequestFactory';
import { useWindowSize } from '@/hooks/useWindowSize';
import { THotWallet } from '@/types/wallet';
import { formatShortAddress, formatUnixTimestamp } from '@/utils/format';
import { CopyIcon } from '@/icons';
import { copyToClipboard } from '@/utils/helper';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/index';

const ListHotWallet = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();
  const networks = useSelector((state: RootState) => state.metadata.networks);

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('WalletRequest').getHotWallet(params);
      return { data: res?.docs, cursor: res?.cursor };
    } catch (e: any) {
      console.error('Get Hot Wallet Error', e?.message);
      return { data: [], cursor: null };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 250;
  }, [windowHeight]);

  return (
    <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
      <AppDataTable
        minWidth={1102}
        ref={dataTableRef}
        getData={getData as any}
        height={tableHeight}
        renderHeader={() => (
          <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Wallet ID
            </div>
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Created At
            </div>
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Address
            </div>
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Asset/Network
            </div>
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Type
            </div>
          </div>
        )}
        renderRow={(hotWallet: THotWallet) => {
          const network = networks.find((n) => n.symbol === hotWallet.network);
          return (
            <div
              key={hotWallet.walletId}
              className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
            >
              <div className="text-theme-sm w-[20%] px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                {hotWallet.walletId || '--'}
              </div>
              <div className="text-theme-sm  w-[20%]  px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                {formatUnixTimestamp(hotWallet.createdAt) || '--'}
              </div>
              <div className="text-theme-sm flex w-[20%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                {hotWallet.address ? (
                  <>
                    {formatShortAddress(hotWallet.address, 4, 4)}
                    <CopyIcon
                      onClick={() => copyToClipboard(hotWallet.address)}
                      className="cursor-pointer"
                    />
                  </>
                ) : (
                  '--'
                )}
              </div>
              <div className="w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                <div className="text-theme-sm">
                  {hotWallet.asset || '--'}
                </div>
                <div className="text-theme-xs">
                  {network?.name || '--'}
                </div>
              </div>
              <div className="text-theme-sm flex w-[20%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                {hotWallet.type?.toLowerCase() || '--'}
              </div>
            </div>
          );
        }}
      />
    </div>
  );
};

export default ListHotWallet;
