'use client';
import React, { useMemo, useRef } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import rf from '@/services/RequestFactory';
import { formatUnixTimestamp } from '@/utils/format';
import { useWindowSize } from '@/hooks';
import ButtonAddMarket from './ButtonAddMarket';
import ButtonEditMarket from './ButtonEditMarket';
import Badge from '@/components/ui/badge/Badge';
import { TMarket } from '@/types/market';
import ButtonDeleteMarket from './ButtonDeleteMarket';

const ListMarkets = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const onFetchData = () => {
    if (dataTableRef.current) {
      // Force refresh the data table
      (dataTableRef.current as any)?.refresh();
    }
  };

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('TradingRequest').getMarkets(params);
      return { data: Array.isArray(res) ? res : [], cursor: null };
    } catch (e: any) {
      console.error('Get Trading Markets Error', e?.message);
      return [];
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 320;
  }, [windowHeight]);

  return (
    <>
      <div className="mb-8 flex justify-end">
        <ButtonAddMarket onFetchData={onFetchData} />
      </div>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1102}
          ref={dataTableRef}
          height={tableHeight}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Created At
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Name
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Type
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Status
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Action
                </div>
              </div>
            );
          }}
          renderRow={(market: TMarket) => {
            return (
              <div
                key={market.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(market.createdAt) || '--'}
                </div>
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {market.name || '--'}
                </div>
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {market.type}
                </div>

                <div className="text-theme-sm w-[20%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  <Badge size="sm" color={market.status === 'ACTIVE' ? 'success' : 'error'}>
                    {market.status?.toLowerCase()}
                  </Badge>
                </div>
                <div className="text-theme-sm flex w-[20%] items-center gap-2 px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  <ButtonEditMarket onFetchData={onFetchData} token={market}/>
                  <ButtonDeleteMarket onFetchData={onFetchData} id={market?.id} />
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default ListMarkets;
