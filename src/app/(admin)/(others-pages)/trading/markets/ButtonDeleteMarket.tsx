'use client';

import React from 'react';
import { TrashBinIcon } from '@/icons';
import ConfirmModal from '@/components/ui/modal/ConfirmModal';
import { useModal } from '@/hooks/useModal';
import rf from '@/services/RequestFactory';
import { toastError, toastSuccess } from '@/libs/toast';

const ButtonDeleteMarket = ({ onFetchData, id }: { onFetchData: () => void; id: number }) => {
  const { isOpen, openModal, closeModal } = useModal();

  const onDeleteMarket = async () => {
    try {
      await rf.getRequest('TradingRequest').deleteMarket(id);
      onFetchData();
      closeModal();
      toastSuccess('Success', 'Delete successfully!');
    } catch (e: any) {
      console.error('Delete Market Error', e?.message);
      toastError('Error', e?.message || 'Something went wrong!');
    }
  };
  return (
    <>
      <div
        onClick={openModal}
        className="cursor-pointer hover:text-red-500 dark:hover:text-red-400"
      >
        <TrashBinIcon />
      </div>

      {isOpen && (
        <ConfirmModal
          isOpen={isOpen}
          onClose={closeModal}
          onConfirm={onDeleteMarket}
          title="Delete Market"
          message="Do you want to delete this market?"
        />
      )}
    </>
  );
};

export default ButtonDeleteMarket;
