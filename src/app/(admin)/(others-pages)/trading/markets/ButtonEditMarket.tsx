'use client';

import React from 'react';
import { EditIcon } from '@/icons';
import { useModal } from '@/hooks/useModal';
import ModalEditMarket from '@/modals/ModalEditMarket';
import { TMarket } from '@/types/market';

const ButtonEditMarket = ({ onFetchData, token }: { onFetchData: () => void; token: TMarket }) => {
  const { isOpen, openModal, closeModal } = useModal();
  return (
    <>
      <div
        onClick={openModal}
        className="cursor-pointer hover:text-gray-800 dark:hover:text-gray-200"
      >
        <EditIcon />
      </div>

      {isOpen && (
        <ModalEditMarket
          isOpen={isOpen}
          onClose={closeModal}
          onFetchData={onFetchData}
          token={token}
        />
      )}
    </>
  );
};

export default ButtonEditMarket;
