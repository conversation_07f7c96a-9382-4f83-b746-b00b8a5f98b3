'use client';

import React from 'react';
import { TrashBinIcon } from '@/icons';
import ConfirmModal from '@/components/ui/modal/ConfirmModal';
import { useModal } from '@/hooks/useModal';
import rf from '@/services/RequestFactory';
import { toastError, toastSuccess } from '@/libs/toast';

const ButtonDeleteFeeLevel = ({ onFetchData, id }: { onFetchData: () => void; id: number }) => {
  const { isOpen, openModal, closeModal } = useModal();

  const onDeleteFeeLevel = async () => {
    try {
      await rf.getRequest('TradingRequest').deleteFeeLevel(id);
      onFetchData();
      closeModal();
      toastSuccess('Success', 'Delete successfully!');
    } catch (e: any) {
      console.error('Delete Fee Level Error', e?.message);
      toastError('Error', e?.message || 'Something went wrong!');
    }
  };
  return (
    <>
      <div
        onClick={openModal}
        className="cursor-pointer hover:text-red-500 dark:hover:text-red-400"
      >
        <TrashBinIcon />
      </div>

      {isOpen && (
        <ConfirmModal
          isOpen={isOpen}
          onClose={closeModal}
          onConfirm={onDeleteFeeLevel}
          title="Delete Fee Level"
          message="Do you want to delete this fee level?"
        />
      )}
    </>
  );
};

export default ButtonDeleteFeeLevel;
