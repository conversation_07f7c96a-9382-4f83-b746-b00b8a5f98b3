'use client';

import React from 'react';
import { useModal } from '@/hooks/useModal';
import { EditIcon } from '@/icons';
import { TFeeLevel } from '@/types/fee';
import ModalEditFeeLevel from '@/modals/ModalEditFeeLevel';

const ButtonEditFee = ({
  onFetchData,
  feeLevel,
}: {
  onFetchData: () => void;
  feeLevel: TFeeLevel;
}) => {
  const { isOpen, openModal, closeModal } = useModal();

  return (
    <>
      <div
        onClick={openModal}
        className="cursor-pointer hover:text-gray-800 dark:hover:text-gray-200"
      >
        <EditIcon />
      </div>

      {isOpen && (
        <ModalEditFeeLevel
          feeLevel={feeLevel}
          isOpen={isOpen}
          onClose={closeModal}
          onFetchData={onFetchData}
        />
      )}
    </>
  );
};

export default ButtonEditFee;
