'use client';
import React, { useMemo, useRef } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import rf from '@/services/RequestFactory';
import { formatNumberWithCommas, formatUnixTimestamp } from '@/utils/format';
import { useWindowSize } from '@/hooks';
import { TFeeLevel } from '@/types/fee';
import ButtonAddFee from './ButtonAddFee';
import ButtonEditFee from './ButtonEditFee';
import ButtonDeleteFeeLevel from './ButtonDeleteFeeLevel';

const ListFeeLevel = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const onFetchData = () => {
    if (dataTableRef.current) {
      // Force refresh the data table
      (dataTableRef.current as any)?.refresh();
    }
  };

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('TradingRequest').getFeeLevel(params);
      return { data: Array.isArray(res) ? res : [], cursor: null };
    } catch (e: any) {
      console.error('Get Fee Level Error', e?.message);
      return [];
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 320;
  }, [windowHeight]);

  return (
    <>
      <div className="mb-8 flex justify-end">
        <ButtonAddFee onFetchData={onFetchData} />
      </div>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1102}
          ref={dataTableRef}
          height={tableHeight}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[13%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Created At
                </div>
                <div className="text-theme-xs w-[14%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Tier
                </div>
                <div className="text-theme-xs w-[12%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Level
                </div>
                <div className="text-theme-xs w-[13%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Maker Fee
                </div>
                <div className="text-theme-xs w-[13%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Taker Fee
                </div>
                <div className="text-theme-xs w-[18%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Minimum Volume In Last 30Days
                </div>
                <div className="text-theme-xs w-[17%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Action
                </div>
              </div>
            );
          }}
          renderRow={(feeLevel: TFeeLevel) => {
            return (
              <div
                key={feeLevel.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[13%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(feeLevel.createdAt * 1000) || '--'}
                </div>
                <div className="text-theme-sm w-[14%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {feeLevel.label || '--'}
                </div>
                <div className="text-theme-sm w-[12%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {feeLevel.level || 0}
                </div>
                <div className="text-theme-sm w-[13%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatNumberWithCommas(feeLevel.makerFee)}
                </div>
                <div className="text-theme-sm w-[13%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatNumberWithCommas(feeLevel.takerFee)}
                </div>
                <div className="text-theme-sm w-[18%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatNumberWithCommas(feeLevel.minimumVolumeInLast30Days)}
                </div>
                <div className="text-theme-sm flex w-[17%] items-center gap-2 px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  <ButtonEditFee onFetchData={onFetchData} feeLevel={feeLevel} />
                  <ButtonDeleteFeeLevel onFetchData={onFetchData} id={feeLevel.id} />
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default ListFeeLevel;
