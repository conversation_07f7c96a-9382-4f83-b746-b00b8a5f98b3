'use client';

import React from 'react';
import { TrashBinIcon } from '@/icons';
import ConfirmModal from '@/components/ui/modal/ConfirmModal';
import { useModal } from '@/hooks/useModal';
import rf from '@/services/RequestFactory';
import { toastError, toastSuccess } from '@/libs/toast';

const ButtonDeletePair = ({ onFetchData, pairId }: { onFetchData: () => void; pairId: number }) => {
  const { isOpen, openModal, closeModal } = useModal();

  const onDeletePair = async () => {
    try {
      await rf.getRequest('TradingRequest').deletePair(pairId);
      onFetchData();
      closeModal();
      toastSuccess('Success', 'Deleted pair successfully!');
    } catch (e: any) {
      console.error('Delete Pair Error', e?.message);
      toastError('Error', e?.message || 'Something went wrong!');
    }
  };
  return (
    <>
      <div
        onClick={openModal}
        className="cursor-pointer hover:text-red-500 dark:hover:text-red-400"
      >
        <TrashBinIcon />
      </div>

      {isOpen && (
        <ConfirmModal
          isOpen={isOpen}
          onClose={closeModal}
          onConfirm={onDeletePair}
          title="Delete Pair"
          message="Do you want to delete this pair?"
        />
      )}
    </>
  );
};

export default ButtonDeletePair;
