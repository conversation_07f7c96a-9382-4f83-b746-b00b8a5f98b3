'use client';

import { AppDataTable } from '@/components/tables/AppDataTable';
import Badge from '@/components/ui/badge/Badge';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import rf from '@/services/RequestFactory';
import Link from 'next/link';
import { TUser } from '@/types/user';
import { useWindowSize } from '@/hooks/useWindowSize';
import Label from '@/components/form/Label';
import Select from '@/components/form/Select';
import { ChevronDownIcon } from '@/icons';
import Button from '@/components/ui/button/Button';
import SearchInput from '@/components/ui/search/index';

const OPTIONS_KYC_STATUS = [
  { value: 'all', label: 'All' },
  { value: 'verified', label: 'Verified' },
  { value: 'unverified', label: 'Unverified' },
];

const ListUser = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();
  const [kycStatus, setKYCStatus] = useState<string>('all');
  const [search, setSearch] = useState<string>('');

  const getData = useCallback(
    async (params: any) => {
      try {
        const res = await rf.getRequest('AccountRequest').getAccounts({
          ...params,
          kycStatus,
          search,
        });
        return { data: res?.docs, cursor: res?.cursor };
      } catch (e: any) {
        console.error('Get User Error', e?.message);
        return { data: [], cursor: null };
      }
    },
    [kycStatus, search]
  );

  const tableHeight = useMemo(() => {
    return windowHeight - 350;
  }, [windowHeight]);

  const onRefreshData = () => {
    (dataTableRef.current as any)?.refresh();
  };

  return (
    <>
      <div className="mb-8 flex items-end justify-between gap-x-6">
        <div className="flex items-end gap-x-6">
          <div>
            <Label>KYC Status</Label>
            <div className="relative">
              <Select
                options={OPTIONS_KYC_STATUS}
                defaultValue={kycStatus}
                onChange={(value: string) => setKYCStatus(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
          <SearchInput
            setSearch={setSearch}
            placeholder="Search by user ID, email, referral ID"
            className="min-w-[300px]"
          />
        </div>

        <Button size="sm" onClick={onRefreshData} className="h-max">
          Refresh
        </Button>
      </div>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1102}
          ref={dataTableRef}
          getData={getData as any}
          height={tableHeight}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  ID
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  User
                </div>
                <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Referral Code
                </div>
                <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  KYC Status
                </div>
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Register At
                </div>
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Refer By
                </div>
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Last Login
                </div>
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Status
                </div>
              </div>
            );
          }}
          renderRow={(user: TUser) => {
            return (
              <Link href={`/users/${user.id}`} key={user.id}>
                <div className="flex w-full cursor-pointer items-center border-b border-gray-100 dark:border-white/[0.05]">
                  <div className="text-theme-sm block w-[10%] px-4 py-3 text-gray-500 dark:text-gray-400">
                    {user.id}
                  </div>
                  <div className="w-[20%] px-4 py-3 text-start">
                    <div className="flex items-center gap-3">
                      <div>
                        {user.name && (
                          <span className="text-theme-sm block font-medium text-gray-800 dark:text-white/90">
                            {user.name || '--'}
                          </span>
                        )}
                        <span className="text-theme-sm block text-gray-500 dark:text-gray-400">
                          {user.email}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-theme-sm w-[15%] items-center px-4 py-3 text-gray-500 dark:text-gray-400">
                    {user.referralCode ?? '--'}
                  </div>
                  <div className="text-theme-sm w-[15%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                    {user.kycStatus === 'verified' ? (
                      <div>
                        {user.kycStatus} ({user.kycLevel || '--'})
                      </div>
                    ) : (
                      <div>{user.kycStatus}</div>
                    )}
                  </div>
                  <div className="text-theme-sm w-[10%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                    --
                  </div>
                  <div className="text-theme-sm w-[10%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                    --
                  </div>
                  <div className="text-theme-sm w-[10%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                    --
                  </div>
                  <div className="text-theme-sm w-[10%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                    <Badge size="sm" color={user.status === 'active' ? 'success' : 'error'}>
                      {user.status}
                    </Badge>
                  </div>
                </div>
              </Link>
            );
          }}
        />
      </div>
    </>
  );
};

export default ListUser;
