import config from '@/config';
import { handleLoginSuccess } from '@/utils/cookie';
import axios, { AxiosError } from 'axios';
import { NextResponse } from 'next/server';

const AUTH_URL = config.apiUrl;

export async function POST(request: Request) {
  console.log('✅ API /api/login hit!');
  try {
    const { email, password } = await request.json();
    console.log('AUTH_URL:', AUTH_URL);

    const { data: authUser } = await axios.post(`${AUTH_URL}/admin/login`, {
      email,
      password,
    });
    const { token } = authUser;
    await handleLoginSuccess(token);

    return NextResponse.json({ success: true, data: authUser });
  } catch (error: any) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;

      const status = axiosError.response?.status || 500;
      const message =
        (axiosError.response?.data as any)?.message || axiosError.message || 'Unexpected error';

      return NextResponse.json({ success: false, error: message }, { status });
    }

    return NextResponse.json(
      { success: false, error: error.message || 'Unknown error occurred' },
      { status: 500 }
    );
  }
}
