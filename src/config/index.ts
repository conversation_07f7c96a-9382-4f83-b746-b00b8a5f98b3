import local from './local.json';
import staging from './staging.json';

export interface Config {
  apiUrl: string;
  apiAppUrl: string;
  apiDexUrl: string;
}

export const envConfig = process.env.NEXT_PUBLIC_ENV || 'local';

interface EnvConfig {
  staging: Config;
  local: Config;
}

const configs: EnvConfig = { local, staging } as EnvConfig;
const config: Config = configs[envConfig as keyof typeof configs];

export default config;
