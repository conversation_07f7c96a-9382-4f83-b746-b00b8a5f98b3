import React, { memo, ReactNode } from 'react';

export const AppButton = memo(
  ({
    children,
    onClick,
    size = 'medium',
    className = '',
    variant = 'primary',
    disabled = false,
    isLoading = false,
  }: {
    children: ReactNode;
    onClick?: () => void;
    className?: string;
    disabled?: boolean;
    isLoading?: boolean;
    variant?: 'primary' | 'secondary' | 'buy' | 'sell' | 'outline';
    size?: 'small' | 'medium' | 'large';
  }) => {
    const getClassByVariant = () => {
      switch (variant) {
        case 'primary': {
          if (disabled || isLoading) {
            return 'bg-white-800 text-black-900 opacity-50';
          }
          return 'bg-white-1000 hover:bg-white-800 text-black-900';
        }
        case 'secondary': {
          if (disabled || isLoading) {
            return 'opacity-50 bg-white-100 hover:bg-white-100';
          }
          return 'bg-white-100 hover:bg-white-50 text-white-1000';
        }
        case 'sell': {
          if (disabled || isLoading) {
            return 'opacity-50 bg-red-500 text-white-1000';
          }
          return 'bg-red-500 hover:bg-red-500 text-white-1000';
        }
        case 'buy': {
          if (disabled || isLoading) {
            return 'opacity-50 bg-green-500 text-white-1000';
          }
          return 'bg-green-500 hover:bg-green-500 text-white-1000';
        }
        case 'outline': {
          return 'hover:bg-white-100 hover:border-white-300 border border-white-150 text-white-1000';
        }
        default: {
          return 'bg-white-1000 hover:bg-white-800 text-black-900';
        }
      }
    };

    const getClassSize = () => {
      switch (size) {
        case 'large': {
          return 'px-[16px] py-[10px] action-sm-medium-14 rounded-[8px]';
        }
        case 'medium': {
          return 'p-[8px] action-xs-medium-12 rounded-[6px]';
        }
        case 'small': {
          return 'p-[4px] action-xs-medium-12 rounded-[4px]';
        }
        default: {
          return 'p-[8px] action-xs-medium-12 rounded-[6px]';
        }
      }
    };

    const handleClick = () => {
      if (disabled || isLoading) {
        return;
      }
      if (onClick) {
        onClick();
      }
    };

    return (
      <div
        onClick={handleClick}
        className={`${getClassSize()} ${
          disabled || isLoading ? 'cursor-not-allowed' : 'cursor-pointer'
        } flex items-center justify-center ${getClassByVariant()} ${className}`}
      >
        {isLoading ? 'Loading...' : children}
      </div>
    );
  }
);

AppButton.displayName = 'AppButton';
