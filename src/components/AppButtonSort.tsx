import { SortIcon, SortReset } from '@/assets/icons';
import * as React from 'react';

export const AppButtonSort = ({
  value,
  setSortBy,
  sortType,
  setSortType,
  sortBy,
}: {
  value: string;
  sortBy: string;
  sortType: string;
  setSortType: (value: string) => void;
  setSortBy: (value: string) => void;
}) => {
  const _renderIcon = () => {
    if (!sortType || value !== sortBy) return <SortReset className="h-4 w-4 text-gray-800" />;
    if (sortType === 'desc') return <SortIcon className="h-4 w-4 rotate-[180deg] text-gray-800" />;
    if (sortType === 'asc') return <SortIcon className="h-4 w-4 text-gray-800" />;

    return <SortReset className="h-4 w-4 text-gray-800" />;
  };

  const onSort = () => {
    if (value !== sortBy) {
      setSortType('desc');
      setSortBy(value);
    } else {
      if (sortType === 'asc') {
        setSortType('');
        setSortBy('');
        return;
      }

      if (sortType === 'desc') {
        setSortType('asc');
        return;
      }
      setSortType('desc');
    }
  };

  return (
    <div onClick={onSort} className="cursor-pointer">
      {_renderIcon()}
    </div>
  );
};
