'use client';

import React, { useCallback, useMemo, useRef } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import rf from '@/services/RequestFactory';
import { useParams } from 'next/navigation';
import BigNumber from 'bignumber.js';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/index';
import Image from 'next/image';
import { useWindowSize } from '@/hooks/useWindowSize';

const UserBalance = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { userId } = useParams();
  const assets = useSelector((state: RootState) => state.metadata.assets);
  const { windowHeight } = useWindowSize();

  const getData = useCallback(
    async (params: any) => {
      if (!userId) return { data: [], cursor: null };
      try {
        const res = await rf.getRequest('UserRequest').getUserBalance({
          ...params,
          user_id: userId,
        });
        return { data: res?.docs, cursor: res?.cursor };
      } catch (e: any) {
        console.error('Get Balance User Error', e?.message);
        return { data: [], cursor: null };
      }
    },
    [userId]
  );

  const tableHeight = useMemo(() => {
    return windowHeight - 350;
  }, [windowHeight]);

  return (
    <>
      <div className="overflow-hidden">
        <AppDataTable
          minWidth={1000}
          ref={dataTableRef}
          getData={getData as any}
          height={tableHeight}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                {['Asset', 'Amount Available', 'Lock', 'Total'].map((label, i) => (
                  <div
                    key={i}
                    className="text-theme-xs w-[25%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                  >
                    {label}
                  </div>
                ))}
              </div>
            );
          }}
          renderRow={(balance: any) => {
            const asset = assets.find((a) => a.id === balance?.asset_id);
            return (
              <div
                key={balance.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[25%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  <div className="flex items-center gap-3">
                    {asset?.logoUrl && (
                      <div className="h-8 w-8 overflow-hidden rounded-full">
                        <Image width={32} height={32} src={asset?.logoUrl} alt={asset?.symbol} />
                      </div>
                    )}
                    <div>
                      <span className="text-theme-sm block font-medium text-gray-800 dark:text-white/90">
                        {asset?.symbol || '--'}
                      </span>
                      <span className="text-theme-xs block text-gray-500 dark:text-gray-400">
                        {asset?.name || '--'}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="text-theme-sm w-[25%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {new BigNumber(balance?.available).toFormat()}
                </div>
                <div className="text-theme-sm w-[25%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {new BigNumber(balance?.locked).toFormat()}
                </div>
                <div className="text-theme-sm w-[25%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {new BigNumber(balance?.total).toFormat()}
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default UserBalance;
