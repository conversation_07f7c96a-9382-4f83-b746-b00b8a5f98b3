'use client';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import { formatUnixTimestamp } from '@/utils/format';
import rf from '@/services/RequestFactory';
import { filterParams } from '@/utils/helper';
import { ChevronDownIcon } from '@/icons';
import Label from '@/components/form/Label';
import Select from '@/components/form/Select';
import Button from '@/components/ui/button/Button';
import { useWindowSize } from '@/hooks/useWindowSize';
import { TOpenOrder } from '@/types/order';
import BigNumber from 'bignumber.js';
import { calculateFilledPercent, getOrderTypeDisplay } from '@/utils/helper';
import { EOrderSide } from '@/types/order';
import SearchInput from '@/components/ui/search/index';
import { useParams } from 'next/navigation';

const OPTIONS_SIDE = [
  { value: '', label: 'All' },
  { value: 'Buy', label: 'Buy' },
  { value: 'Sell', label: 'Sell' },
];

const OPTIONS_ORDER_TYPE = [
  { value: '', label: 'All' },
  { value: 'Limit', label: 'Limit' },
  { value: 'Market', label: 'Market' },
  { value: 'STOP_LIMIT', label: 'Stop Limit' },
  { value: 'STOP_MARKET', label: 'Stop Market' },
];

const TableOpenOrder = () => {
  const { userId } = useParams();
  const [side, setSide] = useState<string>('');
  const [orderType, setOrderType] = useState<string>('');
  const [symbol, setSymbol] = useState<string>('');
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const getData = useCallback(
    async (params: any) => {
      if (!userId) return { cursor: null, data: [] };
      try {
        const result = await rf.getRequest('TradingRequest').getOpenOrder(
          filterParams({
            ...params,
            side,
            userId: +userId,
            orderType,
            symbol,
          })
        );

        return {
          cursor: result?.cursor,
          data: result?.docs || [],
        };
      } catch (err) {
        console.log(err, 'get Withdraw Histories Wallet error');
        return { cursor: null, data: [] };
      }
    },
    [side, orderType, symbol]
  );

  const onRefreshData = () => {
    (dataTableRef.current as any)?.refresh();
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 450;
  }, [windowHeight]);

  return (
    <>
      <div className="mb-8 flex items-end justify-between gap-x-6">
        <div className="flex items-end gap-x-6">
          <div>
            <Label>Side</Label>
            <div className="relative">
              <Select
                options={OPTIONS_SIDE}
                defaultValue={side}
                onChange={(value: string) => setSide(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
          <div>
            <Label>Order Type</Label>
            <div className="relative">
              <Select
                options={OPTIONS_ORDER_TYPE}
                defaultValue={orderType}
                onChange={(value: string) => setOrderType(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
          <SearchInput setSearch={setSymbol} placeholder="Search by Symbol" />
        </div>

        <Button size="sm" onClick={onRefreshData} className="h-max">
          Refresh
        </Button>
      </div>
      <div className="overflow-hidden">
        <AppDataTable
          height={tableHeight}
          minWidth={1250}
          ref={dataTableRef}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[11%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Date
                </div>
                <div className="text-theme-xs w-[11%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Pair
                </div>
                <div className="text-theme-xs w-[11%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Type
                </div>
                <div className="text-theme-xs w-[11%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Side
                </div>
                <div className="text-theme-xs w-[11%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Price
                </div>
                <div className="text-theme-xs w-[11%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Amount
                </div>
                <div className="text-theme-xs w-[11%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Filled
                </div>
                <div className="text-theme-xs w-[11%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Total
                </div>
              </div>
            );
          }}
          renderRow={(order: TOpenOrder) => {
            const getTotal = () => {
              return BigNumber(order?.price || 0)
                .multipliedBy(order?.origQty || 0)
                .toFixed();
            };

            return (
              <div
                key={order.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[11%] items-center px-4 py-3 text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(order.createdAt)}
                </div>
                <div className="text-theme-sm flex w-[11%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {order.baseAssetSymbol}/{order.quoteAssetSymbol}
                </div>
                <div className="text-theme-sm w-[11%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {getOrderTypeDisplay(order.orderType)}
                </div>
                <div className="text-theme-sm flex w-[11%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  <span
                    className={order.side === EOrderSide.BUY ? 'text-green-400' : 'text-red-400'}
                  >
                    {order.side?.toLowerCase()}
                  </span>
                </div>
                <div className="text-theme-sm flex w-[11%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {new BigNumber(order.price).toFormat()}
                </div>
                <div className="text-theme-sm flex w-[11%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {new BigNumber(order.origQty).toFormat()}
                </div>
                <div className="text-theme-sm flex w-[11%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {calculateFilledPercent(order.executedQty, order.origQty)}%
                </div>
                <div className="text-theme-sm flex w-[11%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {getTotal()}
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default TableOpenOrder;
