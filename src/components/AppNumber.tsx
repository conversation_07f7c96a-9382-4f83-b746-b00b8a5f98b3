import { formatNumber } from '@/utils/format';
import BigNumber from 'bignumber.js';
import { memo } from 'react';

const AppNumberForUSD = memo(
  ({
    value,
    isFormatLargeNumber,
    className = '',
  }: {
    value: number | BigNumber | string;
    isFormatLargeNumber: boolean;
    className?: string;
  }) => {
    const bigValue = new BigNumber(value);
    const isNegative = bigValue.isLessThan(0);
    const absValue = bigValue.abs();

    if (absValue.isGreaterThan(1)) {
      return (
        <div className={className} title={formatNumber(value, 2, isFormatLargeNumber)}>
          {isNegative ? '-' : ''}${formatNumber(absValue, 2, isFormatLargeNumber)}
        </div>
      );
    }

    const numStr = absValue.toExponential();
    const exponent = parseInt(numStr.split('e-')[1]);

    if (Number(exponent) >= 1 && Number(exponent) <= 3) {
      return (
        <div title={formatNumber(value, 2, isFormatLargeNumber)} className={className}>
          {isNegative ? '-' : ''}${formatNumber(absValue, exponent - 1 + 4, isFormatLargeNumber)}
        </div>
      );
    }

    if (Number(exponent) > 3) {
      const significantDigits = absValue
        .toFixed()
        .toString()
        .replace(/0+\.0*/, '');

      return (
        <div className={`flex ${className}`} title={formatNumber(value)}>
          {isNegative ? '-' : ''}$0.0
          <div className={`mt-1 text-[10px] ${className}`}>{exponent - 1}</div>
          <div className={className}>{significantDigits.slice(0, 4)}</div>
        </div>
      );
    }

    return (
      <div className={className} title={formatNumber(value, 4, isFormatLargeNumber)}>
        {isNegative ? '-' : ''}${formatNumber(absValue, 4, isFormatLargeNumber)}
      </div>
    );
  }
);

AppNumberForUSD.displayName = 'AppNumberForUSD';

export const AppNumber = memo(
  ({
    value,
    decimals = 4,
    isForUSD = false,
    isFormatLargeNumber = true,
    isHide = false,
    className = '',
  }: {
    value: number | BigNumber | string | undefined;
    decimals?: number;
    isForUSD?: boolean;
    isFormatLargeNumber?: boolean;
    isHide?: boolean;
    className?: string;
  }) => {
    if (!value || new BigNumber(value || 0).isZero() || new BigNumber(value).isNaN()) {
      return <>--</>;
    }

    if (isForUSD) {
      if (isHide) {
        return '******';
      }
      return (
        <AppNumberForUSD
          value={value}
          className={className}
          isFormatLargeNumber={isFormatLargeNumber}
        />
      );
    }

    const bigValue = new BigNumber(value);
    const isNegative = bigValue.isLessThan(0);
    const absValue = bigValue.abs();
    const numStr = absValue.toExponential();
    const exponent = parseInt(numStr.split('e-')[1]);

    if (Number(exponent) >= 1 && Number(exponent) <= 3) {
      return (
        <div title={formatNumber(value, decimals, isFormatLargeNumber)} className={className}>
          {isNegative ? '-' : ''}
          {formatNumber(absValue, exponent - 1 + 4, isFormatLargeNumber)}
        </div>
      );
    }

    if (Number(exponent) > 3) {
      // const significantDigits = absValue
      //   .toFixed()
      //   .toString()
      //   .replace(/0+\.0*/, "");
      // return (
      //   <div
      //     className={`flex ${className}`}
      //     title={formatNumber(value, decimals, isFormatLargeNumber)}
      //   >
      //     {isNegative ? "-" : ""}0.0
      //     <div className={`mt-1 text-[10px] ${className}`}>{exponent - 1}</div>
      //     <div className={className}>{significantDigits.slice(0, 4)}</div>
      //   </div>
      // );
    }

    return (
      <>
        {isHide ? (
          '******'
        ) : (
          <div title={formatNumber(value, decimals, isFormatLargeNumber)} className={className}>
            {isNegative ? '-' : ''}
            {formatNumber(absValue, decimals, isFormatLargeNumber)}
          </div>
        )}
      </>
    );
  }
);

AppNumber.displayName = 'AppNumber';

export default AppNumber;
