import cx from 'classnames';

const TabBar = ({
  activeTab,
  setActiveTab,
  tabs,
}: {
  activeTab: string;
  tabs: any[];
  setActiveTab: (tab: string) => void;
}) => {
  const handleActiveTab = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <div className="rounded-t-xl border border-gray-200 p-3 dark:border-gray-800">
      <nav className="flex w-max overflow-x-auto rounded-lg bg-gray-100 p-1 dark:bg-gray-900 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-200 dark:[&::-webkit-scrollbar-thumb]:bg-gray-600 [&::-webkit-scrollbar-track]:bg-white dark:[&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar]:h-1.5">
        {tabs.map((tab) => (
          <button
            key={tab.value}
            className={cx(
              activeTab === tab.value
                ? 'shadow-theme-xs inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-medium text-gray-900 transition-colors duration-200 ease-in-out dark:bg-white/[0.03] dark:text-white'
                : 'inline-flex items-center rounded-md bg-transparent px-3 py-2 text-sm font-medium text-gray-500 transition-colors duration-200 ease-in-out hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
            )}
            onClick={() => handleActiveTab(tab.value)}
          >
            {tab.label}
          </button>
        ))}
      </nav>
    </div>
  );
};

export default TabBar;
