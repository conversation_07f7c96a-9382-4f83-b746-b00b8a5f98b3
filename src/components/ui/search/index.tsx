import React, { ChangeEvent, useCallback, useState } from 'react';
import { SearchIcon } from '@/icons';
import _ from 'lodash';

const SearchInput = ({
  setSearch,
  placeholder = 'Search',
  className,
}: {
  setSearch: (value: string) => void;
  placeholder?: string;
  className?: string;
}) => {
  const [value, setValue] = useState<string>('');

  const handleSearch = (value: string) => {
    setSearch(value);
  };

  const debouncedSearch = useCallback(_.debounce(handleSearch, 1000), []);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setValue(value);
    debouncedSearch(value);
  };

  return (
    <div className="relative">
      <span className="pointer-events-none absolute left-4 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
        <SearchIcon />
      </span>
      <input
        value={value}
        onChange={(e) => handleChange(e)}
        type="text"
        placeholder={placeholder}
        className={`dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full truncate rounded-lg border border-gray-200 bg-transparent py-2.5 pl-12 pr-3 text-sm text-gray-800 placeholder:text-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:bg-white/[0.03] dark:text-white/90 dark:placeholder:text-white/30 ${className}`}
      />
    </div>
  );
};

export default SearchInput;
