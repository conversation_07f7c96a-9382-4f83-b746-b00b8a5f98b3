'use client';

import React from 'react';
import { Modal } from './index';
import Button from '../button/Button';

export interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
  variant?: 'danger' | 'warning' | 'info';
  icon?: React.ReactNode;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  isLoading = false,
  variant = 'danger',
  icon,
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'danger':
        return {
          iconBg: 'fill-error-50 dark:fill-error-500/15',
          iconColor: 'text-error-500 dark:text-error-400',
          buttonClass: 'bg-error-500 hover:bg-error-600 text-white',
        };
      case 'warning':
        return {
          iconBg: 'fill-warning-50 dark:fill-warning-500/15',
          iconColor: 'text-warning-500 dark:text-warning-400',
          buttonClass: 'bg-warning-500 hover:bg-warning-600 text-white',
        };
      case 'info':
        return {
          iconBg: 'fill-blue-light-50 dark:fill-blue-light-500/15',
          iconColor: 'text-blue-light-500 dark:text-blue-light-400',
          buttonClass: 'bg-blue-light-500 hover:bg-blue-light-600 text-white',
        };
      default:
        return {
          iconBg: 'fill-error-50 dark:fill-error-500/15',
          iconColor: 'text-error-500 dark:text-error-400',
          buttonClass: 'bg-error-500 hover:bg-error-600 text-white',
        };
    }
  };

  const styles = getVariantStyles();

  const defaultIcon = (
    <>
      <svg
        className={`${styles.iconColor} absolute`}
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {variant === 'danger' && (
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM13 17h-2v-2h2v2zm0-4h-2V7h2v6z"
            fill="currentColor"
          />
        )}
        {variant === 'warning' && (
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"
            fill="currentColor"
          />
        )}
        {variant === 'info' && (
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"
            fill="currentColor"
          />
        )}
      </svg>
    </>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="max-w-[500px] p-6 lg:p-8"
      showCloseButton={false}
    >
      <div className="text-center">
        <div className="z-1 relative mb-6 flex items-center justify-center">
          {icon || defaultIcon}
        </div>

        <h4 className="mb-3 text-xl font-semibold text-gray-800 dark:text-white/90 sm:text-2xl">
          {title}
        </h4>
        <p className="text-sm leading-6 text-gray-500 dark:text-gray-400">{message}</p>

        <div className="mt-7 flex w-full items-center justify-center gap-3">
          <Button size="sm" variant="outline" onClick={onClose} disabled={isLoading}>
            {cancelText}
          </Button>
          <Button
            size="sm"
            variant="primary"
            onClick={onConfirm}
            disabled={isLoading}
            className={styles.buttonClass}
          >
            {isLoading ? 'Loading...' : confirmText}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmModal;
