'use client';

import 'react-modern-drawer/dist/index.css';
import React, { useEffect } from 'react';
import Drawer from 'react-modern-drawer';

interface AppDrawerProps {
  isOpen: boolean;
  toggleDrawer?: () => void;
  children: React.ReactNode;
  className?: string;
  direction?: 'left' | 'right' | 'top' | 'bottom';
  enableOverlay?: boolean;
}

export const AppDrawer = ({
  isOpen,
  toggleDrawer,
  children,
  className,
  direction = 'right',
  enableOverlay = true,
}: AppDrawerProps) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <Drawer
      open={isOpen}
      onClose={toggleDrawer}
      direction={direction}
      className={`!dark:text-gray-400 z-9999 !w-[350px] !bg-white !text-gray-500 dark:!bg-gray-900 dark:!text-white ${className}`}
      enableOverlay={enableOverlay}
    >
      {children}
    </Drawer>
  );
};
