import React from "react";

interface SidebarSkeletonProps {
  isExpanded: boolean;
  isHovered: boolean;
  isMobileOpen: boolean;
}

const SidebarSkeleton: React.FC<SidebarSkeletonProps> = ({
  isExpanded,
  isHovered,
  isMobileOpen,
}) => {
  const showFullSkeleton = isExpanded || isHovered || isMobileOpen;

  return (
    <div className="mb-6">
      <div className="flex flex-col gap-4">
        {/* Menu Section */}
        <div>
          {/* Menu Header Skeleton */}
          <div
            className={`mb-4 flex ${
              !isExpanded && !isHovered ? "lg:justify-center" : "justify-start"
            }`}
          >
            {showFullSkeleton ? (
              <div className="h-4 w-12 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
            ) : (
              <div className="h-4 w-4 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
            )}
          </div>

          {/* Navigation Items Skeleton */}
          <div className="flex flex-col gap-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="flex flex-col">
                {/* Main Item Skeleton */}
                <div
                  className={`flex items-center gap-3 rounded-lg p-3 ${
                    !isExpanded && !isHovered
                      ? "lg:justify-center"
                      : "lg:justify-start"
                  }`}
                >
                  {/* Icon Skeleton */}
                  <div className="h-5 w-5 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
                  
                  {/* Text Skeleton */}
                  {showFullSkeleton && (
                    <div className="h-4 w-24 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
                  )}
                </div>

                {/* Sub-items Skeleton (for some items) */}
                {showFullSkeleton && index % 3 === 0 && (
                  <div className="ml-8 mt-2 flex flex-col gap-2">
                    {Array.from({ length: 2 + (index % 3) }).map((_, subIndex) => (
                      <div
                        key={subIndex}
                        className="flex items-center gap-2 rounded-md p-2"
                      >
                        <div className="h-3 w-20 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Others Section Skeleton */}
        {showFullSkeleton && (
          <div>
            {/* Others Header Skeleton */}
            <div className="mb-4 flex justify-start">
              <div className="h-4 w-16 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
            </div>

            {/* Others Items Skeleton */}
            <div className="flex flex-col gap-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="flex flex-col">
                  {/* Main Item Skeleton */}
                  <div className="flex items-center gap-3 rounded-lg p-3">
                    <div className="h-5 w-5 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
                    <div className="h-4 w-20 animate-pulse rounded bg-gray-300 dark:bg-gray-600"></div>
                  </div>

                  {/* Sub-items Skeleton */}
                  <div className="ml-8 mt-2 flex flex-col gap-2">
                    {Array.from({ length: 2 + index }).map((_, subIndex) => (
                      <div
                        key={subIndex}
                        className="flex items-center gap-2 rounded-md p-2"
                      >
                        <div className="h-3 w-16 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SidebarSkeleton;
