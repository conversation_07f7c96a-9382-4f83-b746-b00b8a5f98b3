'use client';

import React, { useState } from 'react';
import { AppPopover } from './AppPopover';
import { ChevronDownIcon, CheckIcon } from '@/assets/icons';
import cx from 'classnames';

interface AppSelectFilterProps {
  options: any[];
  value: string;
  setValue: (value: string) => void;
  title?: string;
}

export const AppSelectFilter = ({ options, value, setValue, title }: AppSelectFilterProps) => {
  const [isShow, setIsShow] = useState<boolean>(false);

  const optionSelected = options.find((item: any) => item.value === value) as any;

  return (
    <AppPopover
      onClose={() => setIsShow(false)}
      customClassWrapper="w-full"
      trigger={
        <div className="border-white-100 flex w-full cursor-pointer items-center justify-between gap-2 rounded-[6px] border p-2 dark:border-gray-700 dark:bg-gray-800">
          {title && (
            <div className="body-sm-regular-12 text-white-300 dark:text-gray-400">{title}</div>
          )}

          <div
            className={`body-md-regular-14 flex flex-1 items-center  gap-2 truncate ${
              title ? 'justify-end' : 'flex w-full justify-between'
            } dark:text-gray-400`}
          >
            {optionSelected?.label}
            <ChevronDownIcon />
          </div>
        </div>
      }
      content={
        <div
          style={{
            boxShadow: '4px 4px 8px 0px rgba(8, 9, 12, 0.50)',
          }}
          className={cx(
            'customer-scroll flex max-h-[300px] w-full flex-col justify-between gap-1 overflow-y-auto rounded-[8px] p-1',
            'dark:bg-gray-800 dark:text-gray-400'
          )}
        >
          {options?.map((item: any, index) => {
            return (
              <div
                onClick={() => {
                  setValue(item.value);
                  setIsShow(false);
                }}
                key={index}
                className={`body-sm-regular-12 hover:bg-white-100 flex w-full cursor-pointer justify-between rounded-[6px] px-1.5 py-2 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-400`}
              >
                {item.label}

                {value === item.value && <CheckIcon />}
              </div>
            );
          })}
        </div>
      }
      isOpen={isShow}
      onToggle={() => setIsShow(!isShow)}
    />
  );
};
