'use client';

import { get } from 'lodash';
import React, {
  CSSProperties,
  forwardRef,
  ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Virtuoso } from 'react-virtuoso';
import clsx from 'clsx';
interface AppDataTableProps<T> {
  ref?: React.RefObject<HTMLDivElement>;
  getData: (params: {
    cursor: string | null;
    limit: number;
  }) => Promise<{ data: T[]; cursor: string | null }>;
  renderHeader: () => ReactNode;
  renderRow: (item: T, index: number) => ReactNode;
  height?: number | string;
  limit?: number;
  containerStyle?: CSSProperties;
  noDataMessage?: string;
  minWidth?: number;
  minHeight?: number;
  shouldAutoFetchOnInit?: boolean;
  onInitializationDone?: () => void;
}

export const AppDataTable = forwardRef<HTMLDivElement, AppDataTableProps<any>>(
  (
    {
      getData,
      renderHeader,
      renderRow,
      onInitializationDone,
      height = 400,
      limit = 20,
      containerStyle,
      noDataMessage = 'No records found.',
      minWidth = 900,
      minHeight,
      shouldAutoFetchOnInit = true,
    },
    ref
  ) => {
    const [items, setItems] = useState<any[]>([]);
    const cursorRef = useRef<string | null>(null);
    const [isEmptyData, setIsEmptyData] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const filterParamsRef = useRef<any>(null);

    useImperativeHandle(
      ref,
      () => {
        return {
          isLoading() {
            return isLoading;
          },

          async refresh() {
            cursorRef.current = null;
            await fetchNewData();
          },

          async appendNewData(newData: any) {
            setItems((prevItems: any[]) => [newData, ...prevItems]);
          },

          async filter(filterParams: any) {
            filterParamsRef.current = filterParams;
            cursorRef.current = null;
            await fetchNewData();
          },

          updateOne(updatedData: any, fieldKey: string, update: any) {
            if (!fieldKey) {
              console.warn('Warning: fieldKey is required for updating an item');
              return;
            }
            setItems((prevItems) => {
              const updatedItems = prevItems.map((item) => {
                if (get(item, fieldKey) === get(updatedData, fieldKey)) {
                  const updatedItem = update(item, updatedData);
                  return updatedItem || item;
                }
                return item;
              });
              return updatedItems;
            });
          },

          removeItem(key: string, value: any) {
            setItems((prev) => {
              return prev.filter((item) => item[key] != value);
            });
          },

          removeAll() {
            setItems([]);
          },

          getItems() {
            return items;
          },
        } as any;
      },
      // eslint-disable-next-line
      [getData]
    );

    const fetchNewData = async () => {
      setIsLoading(true);
      const { data, cursor: newCursor } = await getData({
        cursor: cursorRef?.current || null,
        limit,
        ...filterParamsRef.current,
      });

      setItems(data);
      setIsEmptyData(data?.length < limit);
      cursorRef.current = newCursor;
      setIsLoading(false);
      if (onInitializationDone) {
        onInitializationDone();
      }
    };

    useEffect(() => {
      if (!shouldAutoFetchOnInit) return;
      cursorRef.current = null;
      fetchNewData().then();
      // eslint-disable-next-line
    }, [shouldAutoFetchOnInit, getData]);

    const loadMore = async () => {
      if (isEmptyData || isLoading) return;

      try {
        const { data, cursor: newCursor } = await getData({
          cursor: cursorRef?.current || null,
          limit,
          ...filterParamsRef.current,
        });
        setItems((prevItems) => [...prevItems, ...data]);
        setIsEmptyData(data?.length < limit);
        cursorRef.current = newCursor;
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    const renderedHeader = useMemo(() => {
      return renderHeader();
    }, [renderHeader]);

    const renderContentTable = useCallback(() => {
      if (isLoading) {
        return (
          <>
            <div
              style={{
                height,
                minHeight,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              className="text-gray-500 dark:text-gray-400"
            >
              Loading...
            </div>
          </>
        );
      }

      if (items?.length === 0) {
        return (
          <div
            style={{
              height,
              minHeight,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <div className="flex h-full flex-col items-center justify-center">
              <div className="text-gray-500 dark:text-gray-400">{noDataMessage}</div>
            </div>
          </div>
        );
      }

      return (
        <Virtuoso
          className="customer-scroll"
          style={{
            height,
            minHeight,
          }}
          data={items}
          endReached={loadMore}
          itemContent={(index, item) => <>{renderRow(item, index)}</>}
        />
      );
    }, [isLoading, items, renderRow, loadMore]);

    return (
      <div className="max-w-full overflow-x-auto">
        <div ref={ref} className={clsx(`min-w-[${minWidth}px]`)} style={containerStyle}>
          {renderedHeader !== null && <>{renderedHeader}</>}

          {renderContentTable()}
        </div>
      </div>
    );
  }
);

AppDataTable.displayName = 'AppDataTable';
