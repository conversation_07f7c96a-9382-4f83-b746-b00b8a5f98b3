'use client';

import Input from '@/components/form/input/InputField';
import Label from '@/components/form/Label';
import Button from '@/components/ui/button/Button';
import { EyeCloseIcon, EyeIcon } from '@/icons';
import React, { useState } from 'react';
import Image from 'next/image';
import { toastError } from '@/libs/toast';
import rf from '@/services/RequestFactory';
import { toastSuccess } from '@/libs/toast';
import { useDispatch } from 'react-redux';
import { setUserInfo } from '@/store/user.store';
import { useRouter } from 'next/navigation';

export default function SignInForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const dispatch = useDispatch();
  const router = useRouter();

  const onLogin = async () => {
    try {
      setIsLoading(true);
      const response = await rf.getRequest('AuthRequest').login({
        email,
        password,
      });
      if (!response?.success) {
        toastError('Error', response?.response?.data?.error || 'Login Error');
        setIsLoading(false);
        return;
      }
      // Set basic user info from login response
      dispatch(
        setUserInfo({
          user: {
            name: response?.data?.name,
            email: response?.data?.email,
            role: response?.data?.role,
          },
        })
      );

      router.prefetch('/');
      toastSuccess('Success', 'Login Successfully!');
      setIsLoading(false);
    } catch (e: any) {
      toastError('Error', e?.message || 'Login Error');
      setIsLoading(false);
      console.error(e?.message || 'Login Error');
    }
  };

  return (
    <div className="flex w-full flex-1 flex-col lg:w-1/2">
      <div className="mx-auto flex w-full max-w-md flex-1 flex-col justify-center ">
        <div className="mb-10 block flex items-center justify-center lg:hidden">
          <Image width={160} height={40} src="./images/logo/logo-icon1.svg" alt="Logo" />
        </div>
        <div>
          <div className="mb-5 sm:mb-8">
            <h1 className="text-title-sm sm:text-title-md mb-2 font-semibold text-gray-800 dark:text-white/90">
              Sign In
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Enter your email and password to sign in!
            </p>
          </div>
          <div>
            <div className="space-y-6">
              <div>
                <Label>
                  Email <span className="text-error-500">*</span>{' '}
                </Label>
                <Input
                  defaultValue={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  type="email"
                />
              </div>
              <div>
                <Label>
                  Password <span className="text-error-500">*</span>{' '}
                </Label>
                <div className="relative">
                  <Input
                    defaultValue={password}
                    onChange={(e) => setPassword(e.target.value)}
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                  />
                  <span
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-1/2 z-30 -translate-y-1/2 cursor-pointer"
                  >
                    {showPassword ? (
                      <EyeIcon className="fill-gray-500 dark:fill-gray-400" />
                    ) : (
                      <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400" />
                    )}
                  </span>
                </div>
              </div>
              <div>
                <Button
                  onClick={onLogin}
                  className="w-full"
                  size="sm"
                  disabled={!email || !password || isLoading}
                >
                  Sign in
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
