export type TUserTrade = {
  commission?: string;
  commissionAsset?: string;
  id: number;
  isBestMatch?: boolean;
  isBuyer: boolean;
  isMaker: boolean;
  orderId: number;
  price: string;
  qty: string;
  quoteQty: string;
  symbol: string;
  time: number;
  fee: string;
};

export type TAggTrade = {
  aggTradeId: number;
  createdAt: number;
  firstTradeId: number;
  isBuyerMaker: boolean;
  lastTradeId: number;
  price: string;
  quantity: string;
  symbol: string;
  timestamp: number;
  updatedAt: number;
  isBuyer: boolean; // Indicates if the buyer is the maker
  isMaker: boolean; // Indicates if the trade is from a maker order
};

export enum ETradeRole {
  MAKER = 'Maker',
  TAKER = 'Taker',
}

export enum EOrderSideParam {
  All = 'All',
  BUY = 'Buy',
  SELL = 'Sell',
}

export const OPTIONS_SIDE = [
  {
    label: 'All',
    value: EOrderSideParam.All,
  },
  {
    label: 'Buy',
    value: EOrderSideParam.BUY,
  },
  {
    label: 'Sell',
    value: EOrderSideParam.SELL,
  },
];

export enum EOrderTypeParam {
  All = 'All',
  LIMIT = 'Limit',
  MARKET = 'Market',
  STOP_LIMIT = 'StopLimit',
  STOP_MARKET = 'StopMarket',
}

export const OPTIONS_ORDER_TYPE = [
  {
    label: 'All',
    value: EOrderTypeParam.All,
  },
  {
    label: 'Limit',
    value: EOrderTypeParam.LIMIT,
  },
  {
    label: 'Market',
    value: EOrderTypeParam.MARKET,
  },
  {
    label: 'Stop Limit',
    value: EOrderTypeParam.STOP_LIMIT,
  },
  {
    label: 'Stop Market',
    value: EOrderTypeParam.STOP_MARKET,
  },
];
