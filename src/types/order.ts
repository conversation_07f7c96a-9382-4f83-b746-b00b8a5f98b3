export enum EOrderSide {
  BUY = 'BUY',
  SELL = 'SELL',
}

export enum EOrderType {
  LIMIT = 'LIMIT',
  MARKET = 'MARKET',
  STOP_LIMIT = 'STOP_LIMIT',
  STOP_MARKET = 'STOP_MARKET',
}

export type TOpenOrder = {
  id: number;
  orderId: string;
  userId: number;
  symbol: string;
  pairId: number;
  baseAssetSymbol: string;
  baseAssetId: number;
  quoteAssetSymbol: string;
  quoteAssetId: number;
  side: string;
  orderType: string;
  status: string;
  timeInForce: string;
  price: string;
  stopPrice: null;
  stopCondition: null;
  triggeredAt: null;
  origQty: string;
  executedQty: string;
  cummulativeQuoteQty: string;
  maxQuoteAmount: null;
  operationId: number;
  meOutputAt: number;
  workingTime: number;
  cancelledAt: null;
  errorMessage: null;
  timestamp: number;
  createdAt: number;
  updatedAt: number;
};

export type TOrderHistory = {
  id: number;
  orderId: string;
  userId: number;
  symbol: string;
  pairId: number;
  baseAssetSymbol: string;
  baseAssetId: number;
  quoteAssetSymbol: string;
  quoteAssetId: number;
  side: string;
  orderType: string;
  status: string;
  timeInForce: string;
  price: string;
  stopPrice: null;
  stopCondition: null;
  triggeredAt: null;
  origQty: string;
  executedQty: string;
  cummulativeQuoteQty: string;
  maxQuoteAmount: null;
  operationId: number;
  meOutputAt: number;
  workingTime: number;
  cancelledAt: number;
  errorMessage: null;
  timestamp: number;
  createdAt: number;
  updatedAt: number;
};

export type TTradeHistory = {
  id: number;
  tradeId: number;
  symbol: string;
  pairId: number;
  baseAssetSymbol: string;
  baseAssetId: number;
  quoteAssetSymbol: string;
  quoteAssetId: number;
  price: string;
  qty: string;
  quoteQty: string;
  isBuyerMaker: false;
  takerId: number;
  makerId: number;
  buyOrderId: number;
  sellOrderId: number;
  takerFee: string;
  makerFee: string;
  timestamp: number;
  workingTime: number;
  createdAt: number;
  updatedAt: number;
};
