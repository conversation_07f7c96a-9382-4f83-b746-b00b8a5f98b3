export type TAsset = {
  createdAt: number;
  decimals: number;
  isNative: boolean;
  logoUrl: string;
  name: string;
  symbol: string;
  updatedAt: number;
  available?: string;
  totalBalanceInUsd?: string;
  availableBalanceInUsd?: string;
  price?: string;
  change24h?: string;
  tradingPair?: string;
  id: number;
};

export type TAssetConfig = {
  assetId: number;
  contractAddress: string;
  depositEnabled: boolean;
  depositFee: string;
  id: number;
  minDeposit: string;
  minWithdraw: string;
  networkId: number;
  withdrawEnabled: boolean;
  withdrawFee: string;
  creditedConfirmation: number;
  confirmType: number;
  unlockedConfirmation: number;
  estimatedDepositTime: number;
  estimatedArrivalTime: number;
};
