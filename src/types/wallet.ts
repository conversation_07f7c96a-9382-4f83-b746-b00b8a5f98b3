export type TDepositWallet = {
  id: number;
  walletId: number;
  currency: string;
  fromAddress: string;
  toAddress: string;
  txid: string;
  amount: string;
  asset: string;
  network: string;
  blockNumber: number;
  blockTimestamp: number;
  collectStatus: string;
  collectedTxid: string;
  collectedTimestamp: number;
  confirmedAt: number;
  requiredConfirmation: number;
  createdAt: number;
  updatedAt: number;
};

export type TWithdrawWallet = {
  id: number;
  userId: number;
  walletId: number;
  currency: string;
  withdrawalTxId: number;
  txid: string;
  fromAddress: string;
  toAddress: string;
  amount: string;
  asset: string;
  network: string;
  status: string;
  note: string;
  hashCheck: string;
  kmsDataKeyId: number;
  accountWithdrawalId: number;
  createdAt: number;
  updatedAt: number;
};

export type THotWallet = {
  id: number;
  userId: number;
  walletId: number;
  createdAt: number;
  updatedAt: number;
  isExternal: boolean;
  network: string;
  type: string;
  address: string;
  asset: string;
};
