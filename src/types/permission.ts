import { PermissionEnum } from "./enums";

export type TPermission = {
  action: string[];
  resource: PermissionEnum;
};

export type TRoleResponse = {
  created_at: number;
  description: string;
  id: number;
  name: string;
  updated_at: number;
};

export type TRolePermission = {
  permissions: TPermission[];
  role_id: number;
};

export type TRoleDetailRes = {
  permissions: TPermission[];
  role_description: string;
  role_id: number;
  role_name: string;
};
