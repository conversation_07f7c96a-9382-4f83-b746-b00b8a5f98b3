'use client';
import Label from '@/components/form/Label';
import Input from '@/components/form/input/InputField';
import Button from '@/components/ui/button/Button';
import React, { useEffect, useMemo, useState } from 'react';
import { Modal } from '@/components/ui/modal';
import { ChevronDownIcon } from '@/icons';
import Select from '@/components/form/Select';
import { useForm } from 'react-hook-form';
import { TRole } from '@/types/role';
import rf from '@/services/RequestFactory';
import { toastError, toastSuccess } from '@/libs/toast';

const OPTIONS_STATUS = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'INACTIVE', label: 'Inactive' },
];

const ModalEditAdmin = ({ isOpen, onClose, onFetchData, admin }: any) => {
  const [roles, setRoles] = useState<TRole[]>([]);
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      name: '',
      email: '',
      status: '',
      role: '',
    },
  });

  const getRoles = async () => {
    try {
      const res = await rf.getRequest('RoleRequest').getRoles({});
      setRoles(res);
    } catch (e: any) {
      console.error('Get Roles Error', e?.message);
      setRoles([]);
    }
  };

  useEffect(() => {
    getRoles().then();
  }, []);

  const OPTIONS_ROLE = useMemo(() => {
    return roles.map((item) => {
      return {
        value: item.name,
        label: item.name,
      };
    });
  }, [roles]);

  useEffect(() => {
    if (admin) {
      reset({
        name: admin.name,
        email: admin.email,
        status: admin.status,
        role: admin.roles[0],
      });
    }
  }, [admin, reset]);

  const onSubmit = async (data: any) => {
    try {
      await rf.getRequest('AdminRequest').updateAdmin({
        admin_id: admin.admin_id,
        name: data.name,
        status: data.status,
        roles: [data.role],
      });
      onClose();
      toastSuccess('Success', 'Update Admin Successfully!');
      onFetchData();
    } catch (e: any) {
      toastError('Error', e?.message);
      console.log(e?.message, 'Update Admin Error');
    }
  };

  const handleSelectChange = (value: string) => {
    console.log('Selected value:', value);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="m-4 max-w-[700px]">
      <div className="no-scrollbar relative w-full max-w-[700px] overflow-y-auto rounded-3xl bg-white p-4 dark:bg-gray-900 lg:p-11">
        <div className="px-2 pr-14">
          <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
            Edit Admin
          </h4>
        </div>
        <form className="flex flex-col" onSubmit={handleSubmit(onSubmit)}>
          <div className="custom-scrollbar overflow-y-auto px-2 pb-3">
            <div className="mt-7">
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                <div className="col-span-2 lg:col-span-1">
                  <Label>Email Address</Label>
                  <Input
                    disabled
                    {...register('email', { required: true })}
                    type="email"
                    placeholder="Enter your email"
                  />
                  {errors.email && <p className="text-xs text-red-400">This field is required</p>}
                </div>

                <div className="col-span-2 lg:col-span-1">
                  <Label>Name</Label>
                  <Input
                    {...register('name', { required: true })}
                    type="text"
                    placeholder="Enter your name"
                  />
                  {errors.name && <p className="text-xs text-red-400">This field is required</p>}
                </div>

                <div className="col-span-2 lg:col-span-1">
                  <Label>Role</Label>
                  <div className="relative">
                    <Select
                      {...register('role', { required: true })}
                      defaultValue={admin?.roles[0]}
                      options={OPTIONS_ROLE}
                      placeholder="Select an option"
                      onChange={handleSelectChange}
                      className="dark:bg-dark-900"
                    />
                    <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                </div>

                <div className="col-span-2 lg:col-span-1">
                  <Label>Status</Label>
                  <div className="relative">
                    <Select
                      {...register('status', { required: true })}
                      defaultValue={admin?.status}
                      options={OPTIONS_STATUS}
                      placeholder="Select an option"
                      onChange={handleSelectChange}
                      className="dark:bg-dark-900"
                    />
                    <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                  {errors.status && <p className="text-xs text-red-400">This field is required</p>}
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6 flex items-center gap-3 px-2 lg:justify-end">
            <Button size="sm" variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button size="sm" type="submit">
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default ModalEditAdmin;
