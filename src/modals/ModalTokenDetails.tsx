import { AppDrawer } from '../components/ui/modal/AppDrawer';
import React from 'react';
import { TToken } from '../types/token';
import Image from 'next/image';

const ModalTokenDetails = ({
  isOpen,
  onClose,
  token,
}: {
  isOpen: boolean;
  onClose: () => void;
  token: TToken;
}) => {
  return (
    <AppDrawer isOpen={isOpen} toggleDrawer={onClose}>
      <div className="flex items-center justify-between border-b border-gray-100 p-4 dark:border-white/[0.05]">
        Token Details
        <button onClick={onClose}>
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M6.04289 16.5413C5.65237 16.9318 5.65237 17.565 6.04289 17.9555C6.43342 18.346 7.06658 18.346 7.45711 17.9555L11.9987 13.4139L16.5408 17.956C16.9313 18.3466 17.5645 18.3466 17.955 17.956C18.3455 17.5655 18.3455 16.9323 17.955 16.5418L13.4129 11.9997L17.955 7.4576C18.3455 7.06707 18.3455 6.43391 17.955 6.04338C17.5645 5.65286 16.9313 5.65286 16.5408 6.04338L11.9987 10.5855L7.45711 6.0439C7.06658 5.65338 6.43342 5.65338 6.04289 6.0439C5.65237 6.43442 5.65237 7.06759 6.04289 7.45811L10.5845 11.9997L6.04289 16.5413Z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>

      <div className="flex items-center gap-3 p-4">
        {token.logoUrl && (
          <div className="h-8 w-8 overflow-hidden rounded-full">
            <Image width={32} height={32} src={token.logoUrl} alt={token.symbol} />
          </div>
        )}
        <div>
          <div>{token.symbol || '--'}</div>
          <div className="text-theme-xs">{token.name || '--'}</div>
        </div>
      </div>
    </AppDrawer>
  );
};

export default ModalTokenDetails;
