'use client';
import Label from '@/components/form/Label';
import Input from '@/components/form/input/InputField';
import Button from '@/components/ui/button/Button';
import React, { useEffect } from 'react';
import { Modal } from '@/components/ui/modal';
import { useForm } from 'react-hook-form';
import rf from '@/services/RequestFactory';
import { toastSuccess } from '@/libs/toast';
import { TMarket } from '@/types/market';
import Select from '@/components/form/Select';
import { ChevronDownIcon } from '@/icons';

const OPTIONS_TYPE = [{ value: 'SPOT', label: 'Spot' }];

const OPTIONS_STATUS = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'INACTIVE', label: 'Inactive' },
];

const ModalEditMarket = ({
  isOpen,
  onClose,
  onFetchData,
  token,
}: {
  isOpen: boolean;
  onClose: () => void;
  onFetchData: () => void;
  token: TMarket;
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    defaultValues: {
      name: '',
      status: '',
      type: '',
    },
  });

  useEffect(() => {
    if (token) {
      reset({
        name: token.name,
        status: token.status,
        type: token.type,
      });
    }
  }, [token, reset]);

  const onSubmit = async (data: any) => {
    try {
      await rf.getRequest('TradingRequest').updateMarket(token.id, {
        name: data.name,
        status: data.status,
        type: data.type,
      });
      toastSuccess('Success', 'Update Successfully!');
      onClose();
      onFetchData();
    } catch (e: any) {
      console.error('Update Market Error', e?.message);
    }
  };

  const handleChangeType = (value: string) => {
    console.log('Selected value:', value);
  };

  const handleChangeStatus = (value: string) => {
    console.log('Selected value:', value);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="m-4 max-w-[700px]">
      <div className="no-scrollbar relative w-full max-w-[700px] overflow-y-auto rounded-3xl bg-white p-4 dark:bg-gray-900 lg:p-11">
        <div className="px-2 pr-14">
          <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
            Edit Market
          </h4>
        </div>
        <form className="flex flex-col" onSubmit={handleSubmit(onSubmit)}>
          <div className="custom-scrollbar overflow-y-auto px-2 pb-3">
            <div className="mt-7">
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                <div className="col-span-2">
                  <Label>Name</Label>
                  <Input
                    {...register('name', { required: true })}
                    placeholder="Enter Name"
                    error={!!errors.name}
                  />
                  {errors.name && <p className="text-xs text-red-400">This field is required</p>}
                </div>
                <div className="col-span-2 lg:col-span-1">
                  <Label>Type</Label>
                  <div className="relative">
                    <Select
                      {...register('type')}
                      defaultValue={token.type}
                      options={OPTIONS_TYPE}
                      placeholder="Select an option"
                      onChange={handleChangeType}
                      className="dark:bg-dark-900"
                    />
                    <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                </div>
                <div className="col-span-2 lg:col-span-1">
                  <Label>Status</Label>
                  <div className="relative">
                    <Select
                      {...register('status')}
                      defaultValue={token.status}
                      options={OPTIONS_STATUS}
                      placeholder="Select an option"
                      onChange={handleChangeStatus}
                      className="dark:bg-dark-900"
                    />
                    <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6 flex items-center gap-3 px-2 lg:justify-end">
            <Button size="sm" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Close
            </Button>
            <Button size="sm" type="submit" disabled={isSubmitting}>
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default ModalEditMarket;
