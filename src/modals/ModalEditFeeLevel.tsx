'use client';
import Label from '../components/form/Label';
import Input from '../components/form/input/InputField';
import Button from '../components/ui/button/Button';
import React, { useEffect } from 'react';
import { Modal } from '../components/ui/modal';
import { useForm } from 'react-hook-form';
import rf from '@/services/RequestFactory';
import { toastSuccess } from '../libs/toast';
import { TFeeLevel } from '@/types/fee';
import BigNumber from 'bignumber.js';

const ModalEditFeeLevel = ({
  isOpen,
  onClose,
  onFetchData,
  feeLevel,
}: {
  isOpen: boolean;
  onClose: () => void;
  onFetchData: () => void;
  feeLevel: TFeeLevel;
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    defaultValues: {
      label: '',
      makerFee: '',
      takerFee: '',
      minimumVolumeInLast30Days: '',
    },
  });

  useEffect(() => {
    if (feeLevel) {
      reset({
        label: feeLevel.label,
        makerFee: feeLevel.makerFee,
        takerFee: feeLevel.takerFee,
        minimumVolumeInLast30Days: new BigNumber(feeLevel.minimumVolumeInLast30Days).toFixed(),
      });
    }
  }, [feeLevel, reset]);

  const onSubmit = async (data: any) => {
    try {
      await rf.getRequest('TradingRequest').updateFeeLevel(feeLevel.level, {
        label: data.label,
        makerFee: data.makerFee,
        takerFee: data.takerFee,
        minimumVolumeInLast30Days: data.minimumVolumeInLast30Days,
      });
      toastSuccess('Success', 'Update Fee Level Successfully!');
      onClose();
      onFetchData();
    } catch (e: any) {
      console.error('Update Fee Level Error', e?.message);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="m-4 max-w-[700px]">
      <div className="no-scrollbar relative w-full max-w-[700px] overflow-y-auto rounded-3xl bg-white p-4 dark:bg-gray-900 lg:p-11">
        <div className="px-2 pr-14">
          <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
            Edit Fee Level
          </h4>
        </div>
        <form className="flex flex-col" onSubmit={handleSubmit(onSubmit)}>
          <div className="custom-scrollbar overflow-y-auto px-2 pb-3">
            <div className="mt-7">
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                <div className="col-span-2">
                  <Label>Tier</Label>
                  <Input
                    {...register('label', { required: true })}
                    placeholder="Tier"
                    error={!!errors.label}
                  />
                  {errors.label && <p className="text-xs text-red-400">This field is required</p>}
                </div>
                <div className="col-span-2 lg:col-span-1">
                  <Label>Maker Fee</Label>
                  <Input
                    {...register('makerFee', {
                      required: true,
                    })}
                    type="text"
                    placeholder="Maker Fee"
                    error={!!errors.makerFee}
                  />
                  {errors.makerFee && (
                    <p className="text-xs text-red-400">This field is required</p>
                  )}
                </div>
                <div className="col-span-2 lg:col-span-1">
                  <Label>Taker Fee</Label>
                  <Input
                    {...register('takerFee', {
                      required: true,
                    })}
                    type="text"
                    placeholder="Taker Fee"
                    error={!!errors.takerFee}
                  />
                  {errors.takerFee && (
                    <p className="text-xs text-red-400">This field is required</p>
                  )}
                </div>
                <div className="col-span-2">
                  <Label>Minimum Volume In Last 30Days</Label>
                  <Input
                    {...register('minimumVolumeInLast30Days', {
                      required: true,
                    })}
                    type="text"
                    placeholder="Minimum Volume In Last 30Days"
                    error={!!errors.minimumVolumeInLast30Days}
                  />
                  {errors.minimumVolumeInLast30Days && (
                    <p className="text-xs text-red-400">This field is required</p>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6 flex items-center gap-3 px-2 lg:justify-end">
            <Button size="sm" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Close
            </Button>
            <Button size="sm" type="submit" disabled={isSubmitting}>
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default ModalEditFeeLevel;
