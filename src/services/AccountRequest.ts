import config from '@/config';
import BaseRootRequest from './BaseRequest';

export default class AccountRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  getAccounts(params: any) {
    const url = `/account`;
    return this.get(url, { ...params });
  }

  getAccountDetails(userId: number) {
    const url = `/account/${userId}`;
    return this.get(url);
  }

  getKYCDetails(userId: number) {
    const url = `/account/kycs/${userId}`;
    return this.get(url);
  }

  getTransactionHistories(params: any) {
    const url = `/account/transaction-histories`;
    return this.get(url, params);
  }

  getKycs(params: any) {
    const url = `/account/kycs`;
    return this.get(url, { ...params });
  }
}
