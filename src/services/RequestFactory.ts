import AuthRequest from './AuthRequest';
import AdminRequest from './AdminRequest';
import AccountRequest from './AccountRequest';
import MarketDataRequest from './MarketDataRequest';
import RoleRequest from './RoleRequest';
import UserRequest from './UserRequest';
import PermissionRequest from './PermissionRequest';
import WalletRequest from './WalletRequest';
import TradingRequest from './TradingRequest';

const requestMap = {
  AuthRequest,
  AdminRequest,
  AccountRequest,
  MarketDataRequest,
  RoleRequest,
  UserRequest,
  PermissionRequest,
  WalletRequest,
  TradingRequest,
};

const instances: Partial<Record<RequestKey, InstanceType<RequestMap[RequestKey]>>> = {};

type RequestMap = typeof requestMap;

type RequestKey = keyof RequestMap;

export default class RequestFactory {
  static getRequest<T extends RequestKey>(classname: T): InstanceType<RequestMap[T]> {
    const RequestClass = requestMap[classname];
    if (!RequestClass) {
      throw new Error(`Invalid request class name: ${classname}`);
    }

    let requestInstance = instances[classname];
    if (!requestInstance) {
      requestInstance = new RequestClass();
      instances[classname] = requestInstance;
    }

    return requestInstance as InstanceType<RequestMap[T]>;
  }
}
