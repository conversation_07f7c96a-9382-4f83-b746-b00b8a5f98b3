import config from '@/config';
import BaseRootRequest from './BaseRequest';
import { TPermission, TRoleDetailRes, TRolePermission, TRoleResponse } from '@/types/permission';
import { TRole } from '@/types/role';

export default class PermissionRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  getPermissions(): Promise<TPermission[]> {
    const url = `/permission/permissions`;
    return this.get(url);
  }

  createPermission(params: TRolePermission): Promise<TPermission> {
    const url = `/permission/role-permissions`;
    return this.post(url, params);
  }

  getRolePermission(roleId: string | number): Promise<TRoleDetailRes> {
    const url = `/permission/role-permissions/${roleId}`;
    return this.get(url);
  }

  updateRole(params: TRolePermission): Promise<TRole> {
    const url = `/permission/role-permissions`;
    return this.put(url, params);
  }

  updateRolePermissions(params: TRolePermission): Promise<TPermission> {
    const url = `/permission/role-permissions`;
    return this.put(url, params);
  }
}
