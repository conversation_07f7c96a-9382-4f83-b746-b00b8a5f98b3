import config from '@/config';
import BaseRootRequest from './BaseRequest';

export default class TradingRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  getOpenOrder(params: any) {
    const url = `/trading/openOrders`;
    return this.get(url, params);
  }

  getTrades(params: any) {
    const url = `/trading/trades`;
    return this.get(url, params);
  }

  getOrderHistory(params: any) {
    const url = `/trading/allOrders`;
    return this.get(url, params);
  }

  getPair(params: any) {
    const url = `/market-data/pairs`;
    return this.get(url, params);
  }

  deletePair(pairId: number) {
    const url = `/market-data/pairs/${pairId}`;
    return this.delete(url);
  }

  getFeeLevel(params: any) {
    const url = `/market-data/fee-tiers`;
    return this.get(url, params);
  }

  updateFeeLevel(level: number, data: any) {
    const url = `/market-data/fee-tiers/${level}`;
    return this.put(url, data);
  }

  deleteFeeLevel(id: number) {
    const url = `/market-data/fee-tiers/${id}`;
    return this.delete(url);
  }

  getMarkets(params: any) {
    const url = `/market-data/markets`;
    return this.get(url, params);
  }

  addMarket(data: any) {
    const url = `/market-data/markets`;
    return this.post(url, data);
  }

  updateMarket(id: number, data: any) {
    const url = `/market-data/markets/${id}`;
    return this.put(url, data);
  }

  deleteMarket(id: number) {
    const url = `/market-data/markets/${id}`;
    return this.delete(url);
  }
}
