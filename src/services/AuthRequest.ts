import config from '@/config';
import BaseRootRequest from './BaseRequest';

export default class AuthRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiAppUrl;
  }

  login({ email, password }: { email: string; password: string }) {
    const url = `/api/login`;
    return this.post(url, { email, password });
  }

  logout() {
    const url = `/api/logout`;
    return this.get(url);
  }
}
