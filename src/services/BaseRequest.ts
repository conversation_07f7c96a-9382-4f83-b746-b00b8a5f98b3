import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
// import { load } from 'recaptcha-v3';
import config from '@/config';
import { AppBroadcast, BROADCAST_EVENTS } from '@/libs/broadcast';
// import Storage from '@/libs/storage';
// import { setAuthorizationToRequest } from '@/utils/auth';
// import retry from 'async-retry';

export const setAuthorizationToRequest = (token: string) => {
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
};

export default class BaseRequest {
  protected accessToken = '';
  private isRefreshing = false;
  private refreshSubscribers: ((token: string) => void)[] = [];
  constructor(accessToken?: string | undefined) {
    if (accessToken) {
      this.accessToken = accessToken;
      // setAuthorizationToRequest(accessToken);
    }
  }

  getUrlPrefix() {
    return config.apiUrl;
  }

  async buildCustomHeaders() {
    return {
      'x-request-id': uuidv4(),
    };
    // return retry(
    //   async () => {
    //     try {
    //       const recaptcha = await load(config.reCaptchaSiteKey);
    //       const token = await recaptcha.execute('SUBMIT');
    //       return {
    //         recaptcha: token,
    //       };
    //     } catch (error) {
    //       throw new Error('The RaidenX is busy, please try again later!');
    //     }
    //   },
    //   {
    //     retries: 1,
    //     minTimeout: 1000,
    //     maxTimeout: 2000,
    //   },
    // );
  }

  async _handleRequest(requestFn: () => Promise<any>) {
    try {
      const response = await requestFn();
      return this._responseHandler(response);
    } catch (error) {
      return this._errorHandler(error);
    }
  }

  async get(url: string, params?: any) {
    const config = {
      params,
      headers: await this.buildCustomHeaders(),
    };
    return this._handleRequest(() => axios.get(this.getUrlPrefix() + url, config));
  }

  async getWithoutEncode(url: string, params?: any) {
    const config = {
      params,
      headers: await this.buildCustomHeaders(),
      paramsSerializer: (params: any) => {
        return Object.entries(params)
          .map(([key, value]) => `${key}=${value}`)
          .join('&');
      },
    };
    return this._handleRequest(() => axios.get(this.getUrlPrefix() + url, config));
  }

  async put(url: any, data?: any) {
    const config = {
      headers: await this.buildCustomHeaders(),
    };
    return this._handleRequest(() => axios.put(this.getUrlPrefix() + url, data, config));
  }

  async patch(url: any, data?: any) {
    const config = {
      headers: await this.buildCustomHeaders(),
    };
    return this._handleRequest(() => axios.patch(this.getUrlPrefix() + url, data, config));
  }

  async post(url: any, data = {}) {
    const config = {
      headers: await this.buildCustomHeaders(),
    };
    return this._handleRequest(() => axios.post(this.getUrlPrefix() + url, data, config));
  }

  async delete(url: any, data?: any) {
    const config = {
      data,
      headers: await this.buildCustomHeaders(),
    };
    return this._handleRequest(() => axios.delete(this.getUrlPrefix() + url, config));
  }

  async download(url: any, data?: any) {
    const config = {
      ...data,
      headers: await this.buildCustomHeaders(),
      responseType: 'blob',
    };
    return this._handleRequest(() => axios.get(this.getUrlPrefix() + url, config));
  }

  async _responseHandler(response: any) {
    return response.data;
  }

  // async _error401Handler(originalRequest?: () => Promise<any>) {
  //   if (this.isRefreshing) {
  //     return new Promise<string>((resolve, reject) => {
  //       this.refreshSubscribers.push((token: string) => {
  //         setAuthorizationToRequest(token);
  //         originalRequest!().then(resolve).catch(reject);
  //       });
  //     });
  //   }
  //
  //   this.isRefreshing = true;
  //
  //   try {
  //     const newToken = await this._refreshToken();
  //
  //     this.isRefreshing = false;
  //     this.refreshSubscribers.forEach((callback) => callback(newToken));
  //     this.refreshSubscribers = [];
  //
  //     if (originalRequest) {
  //       setAuthorizationToRequest(newToken);
  //       return originalRequest();
  //     }
  //   } catch (error) {
  //     console.log('401 error', error);
  //     this.isRefreshing = false;
  //     AppBroadcast.dispatch(BROADCAST_EVENTS.LOGOUT, {});
  //     throw new Error('Unauthorized: Refresh token failed.');
  //   }
  // }
  //
  // async _refreshToken() {
  //   try {
  //     const refreshToken = Storage.getRefreshToken();
  //
  //     if (!refreshToken) {
  //       throw new Error('Refresh token not available');
  //     }
  //
  //     console.log('💔💔💔💔💔--------> refreshToken', refreshToken);
  //
  //     setAuthorizationToRequest(refreshToken);
  //
  //     const response = await this.post('/auth/refresh-token');
  //
  //     console.log('💔💔💔💔💔--------> response', response);
  //
  //     const newAccessToken = response.newAccessToken;
  //     const newRefreshToken = response.newRefreshToken;
  //
  //     if (!!newAccessToken || !!newRefreshToken) {
  //       Storage.setAccessToken(newAccessToken);
  //       Storage.setRefreshToken(newRefreshToken);
  //
  //       this.accessToken = newAccessToken;
  //       setAuthorizationToRequest(newAccessToken);
  //
  //       return newAccessToken;
  //     }
  //   } catch (error) {
  //     console.error('Error refreshing token:', error);
  //     throw error;
  //   }
  // }

  _error403Handler() {
    // TODO: make broadcast event
    // AppBroadcast.dispatch(BROADCAST_EVENTS.LOGOUT, {});
  }

  async _errorHandler(err: any) {
    if (err.response?.status === 401) {
      // Handle 401 Unauthorized - trigger logout
      console.log('401 Unauthorized error detected, triggering logout');
      // AppBroadcast.dispatch(BROADCAST_EVENTS.LOGOUT, {});
      throw new Error('Unauthorized: Please log in again.');
    }

    if (err.response?.status === 403) {
      return this._error403Handler();
    }

    if (err.response && err.response.data && err.response.data.data) {
      if (typeof err.response.data.data.message === 'string') {
        throw new Error(err.response.data.data.message);
      }
    }

    if (err.response && err.response.data && err.response.data.message) {
      if (typeof err.response.data.message === 'string') {
        throw new Error(err.response.data.message);
      }
      throw new Error(err.response.data.message[0]);
    }

    if (err.response && err.response.data && err.response.data.error) {
      throw new Error(err.response.data.error);
    }

    if (err.response && err.response.data && typeof err.response.data === 'string') {
      throw new Error(err.response.data);
    }

    throw err;
  }
}
