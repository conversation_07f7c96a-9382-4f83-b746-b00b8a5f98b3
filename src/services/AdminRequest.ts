import config from '@/config';
import BaseRootRequest from './BaseRequest';

export default class AdminRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  getAccountProfile() {
    const url = `/admin/profile`;
    return this.get(url);
  }

  acceptInviteAdmin(params: any) {
    const url = `/admin/accept-invite`;
    return this.post(url, params);
  }

  inviteAdmin(params: any) {
    const url = `/admin/invite`;
    return this.post(url, params);
  }

  createAdmin(params: any) {
    const url = `/admin/create`;
    return this.post(url, params);
  }

  updateAdmin(params: any) {
    const url = `/admin/update`;
    return this.post(url, params);
  }

  listAdmin(params: any) {
    const url = `/admin/list`;
    return this.get(url, params);
  }
}
