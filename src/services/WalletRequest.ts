import config from '@/config';
import BaseRootRequest from './BaseRequest';

export default class WalletRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  getDepositAddresses(params: any) {
    const url = `/wallet/deposit-addresses`;
    return this.get(url, params);
  }

  getDepositHistoryWallet(params: any) {
    const url = `/wallet/deposits`;
    return this.get(url, params);
  }

  getWalletTxInternalTransfers(params: any) {
    const url = `/wallet/internal-transfers`;
    return this.get(url, params);
  }

  getWithdrawHistoryWallet(params: any) {
    const url = `/wallet/withdrawals`;
    return this.get(url, params);
  }

  getColdWallet(params: { cursor?: string; limit?: number; order?: string }) {
    const url = `/wallet/cold-wallets`;
    return this.get(url, params);
  }

  getHotWallet(params: { cursor?: string; limit?: number; order?: string }) {
    const url = `/wallet/hot-wallets`;
    return this.get(url, params);
  }
}
