import config from '@/config';
import BaseRootRequest from './BaseRequest';
import { TRoleResponse } from '@/types/permission';

export default class RoleRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  getRoles(params: any) {
    const url = `/role`;
    return this.get(url, params);
  }

  createRole(params: { description: string; name: string }): Promise<TRoleResponse> {
    const url = `/role`;
    return this.post(url, params);
  }

  deleteRole(roleId: string | number) {
    const url = `/role/${roleId}`;
    return this.delete(url);
  }
}
