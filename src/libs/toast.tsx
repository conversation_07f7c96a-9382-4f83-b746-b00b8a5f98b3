import * as React from 'react';
import { toast } from 'react-toastify';
import Alert from '@/components/ui/alert/Alert';

export const toastSuccess = (
  title: string,
  message: string,
  showLink?: boolean, // Whether to show the "Learn More" link
  linkHref?: string, // Link URL
  linkText?: string // Link text
) => {
  toast(
    React.createElement(Alert, {
      variant: 'success',
      title,
      message,
      showLink,
      linkHref,
      linkText,
    })
  );
};

export const toastError = (
  title: string,
  message: string,
  showLink?: boolean, // Whether to show the "Learn More" link
  linkHref?: string, // Link URL
  linkText?: string // Link text
) => {
  toast(
    React.createElement(Alert, {
      variant: 'error',
      title,
      message,
      showLink,
      linkHref,
      linkText,
    })
  );
};

export const toastInfo = (
  title: string,
  message: string,
  showLink?: boolean, // Whether to show the "Learn More" link
  linkHref?: string, // Link URL
  linkText?: string // Link text
) => {
  toast(
    React.createElement(Alert, {
      variant: 'info',
      title,
      message,
      showLink,
      linkHref,
      linkText,
    })
  );
};

export const toastWarning = (
  title: string,
  message: string,
  showLink?: boolean, // Whether to show the "Learn More" link
  linkHref?: string, // Link URL
  linkText?: string // Link text
) => {
  toast(
    React.createElement(Alert, {
      variant: 'warning',
      title,
      message,
      showLink,
      linkHref,
      linkText,
    })
  );
};
