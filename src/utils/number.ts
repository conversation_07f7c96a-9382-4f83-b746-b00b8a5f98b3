import BigNumber from 'bignumber.js';

BigNumber.config({
  EXPONENTIAL_AT: 18,
  ROUNDING_MODE: BigNumber.ROUND_DOWN,
});

export const multipliedBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber,
  decimals = 15,
  roundMode?: BigNumber.RoundingMode
) => {
  return new BigNumber(
    new BigNumber(a || 0).multipliedBy(b || 0).toFixed(decimals, roundMode)
  ).toString();
};

export const dividedBN = (a: number | string | BigNumber, b: number | string | BigNumber) => {
  return new BigNumber(a || 0).dividedBy(b || 0).toString();
};

export const isZero = (a: number | string | BigNumber | null | undefined) => {
  return new BigNumber(a || 0).isZero();
};

export const plusBN = (a: number | string | BigNumber, b: number | string | BigNumber) => {
  return new BigNumber(a || 0).plus(b || 0).toString();
};

export const minusBN = (a: number | string | BigNumber, b: number | string | BigNumber) => {
  return new BigNumber(a || 0).minus(b || 0).toString();
};
