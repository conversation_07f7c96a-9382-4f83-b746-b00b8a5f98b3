import BigNumber from 'bignumber.js';
import moment from 'moment';
import commaNumber from 'comma-number';
import { SI } from '@/constants';

export function formatNumberWithCommas(
  value: string | number | BigNumber,
  decimalPlaces = 8
): string {
  return commaNumber(new BigNumber(Number(value).toFixed(decimalPlaces)).toString(), ',', '.');
}

const _formatLargeNumberIfNeed = (number: string, digits = 0) => {
  const comparedResult = BigNumber(number).comparedTo(1000);
  if (comparedResult && comparedResult < 0) {
    return formatNumberWithCommas(number, digits);
  }
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  const num = parseFloat(number);
  let i;
  for (i = SI.length - 1; i > 0; i--) {
    if (num >= SI[i].value) {
      break;
    }
  }
  return (
    BigNumber(num / SI[i].value)
      .toFixed(digits, BigNumber.ROUND_DOWN)
      .toString()
      .replace(rx, '$1') + SI[i].symbol
  );
};

export const roundNumber = (
  number: number | string | BigNumber,
  roundMode = BigNumber.ROUND_DOWN,
  decimals = 18
) => {
  const newNumber = new BigNumber(number).toFixed(decimals, roundMode);
  return new BigNumber(newNumber).toString();
};

export function formatNumber(
  value: string | number | BigNumber,
  decimalPlaces = 8,
  isFormatLargeNumber = true,
  defaultValue = '--'
): string {
  if (!value || BigNumber(value || 0).isZero()) {
    return defaultValue;
  }

  if (BigNumber(value).isGreaterThan(0) && BigNumber(value).isLessThan(0.00000001)) {
    return '<' + new BigNumber(0.00000001).toString();
  }

  if (!isFormatLargeNumber) {
    return formatNumberWithCommas(value, decimalPlaces);
  }

  return _formatLargeNumberIfNeed(
    roundNumber(value, BigNumber.ROUND_DOWN, decimalPlaces),
    decimalPlaces
  );
}

export const formatShortAddress = (
  address: string | undefined,
  digits = 8,
  digitsAfter = 6
): string => {
  if (!address) {
    return '--';
  }
  return `${address?.substring(0, digits)}...${address.substring(
    address.length - digitsAfter,
    address.length
  )}`;
};

export const formatUnixTimestamp = (timestamp: number, formatDate = 'YYYY-MM-DD HH:mm:ss') => {
  if (!timestamp) return '--';

  return moment(+timestamp).format(formatDate);
};
