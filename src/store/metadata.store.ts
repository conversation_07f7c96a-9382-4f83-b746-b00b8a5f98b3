import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import rf from '@/services/RequestFactory';
import { TNetwork } from '@/types/network';
import { TAsset } from '@/types/asset';

export type MetadataState = {
  networks: TNetwork[];
  assets: TAsset[];
  loading: boolean;
  error: string | null;
};

const initialState: MetadataState = {
  networks: [],
  assets: [],
  loading: false,
  error: null,
};

export const getNetworks = createAsyncThunk(
  'metadata/getNetworks',
  async (_, { rejectWithValue }) => {
    try {
      const data = await rf.getRequest('MarketDataRequest').getNetworks();
      return data?.data || [];
    } catch (error) {
      return rejectWithValue('Failed to fetch networks');
    }
  }
);

export const getAssets = createAsyncThunk('metadata/getAssets', async (_, { rejectWithValue }) => {
  try {
    const data = await rf.getRequest('MarketDataRequest').getAssets();
    return data?.data || [];
  } catch (error) {
    return rejectWithValue('Failed to fetch assets');
  }
});

const metadataSlice = createSlice({
  name: 'metadata',
  initialState,
  reducers: {
    setNetworks: (state, action) => {
      const { networks } = action.payload;
      state.networks = networks;
    },
    setAssets: (state, action) => {
      const { assets } = action.payload;
      state.assets = assets;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // getNetworks
      .addCase(getNetworks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getNetworks.fulfilled, (state, action) => {
        state.loading = false;
        state.networks = action.payload;
      })
      .addCase(getNetworks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // getAssets
      .addCase(getAssets.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAssets.fulfilled, (state, action) => {
        state.loading = false;
        state.assets = action.payload;
      })
      .addCase(getAssets.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setNetworks, setAssets, clearError } = metadataSlice.actions;

export default metadataSlice.reducer;
