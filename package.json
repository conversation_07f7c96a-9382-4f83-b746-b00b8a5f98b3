{"name": "vdax-admin-interface", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,css,scss,md,json}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,css,scss,md,json}\"", "lint:fix": "next lint --fix", "prepare": "husky"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@reduxjs/toolkit": "^2.7.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.0.9", "apexcharts": "^4.3.0", "autoprefixer": "^10.4.20", "axios": "^1.8.4", "bignumber.js": "^9.3.0", "comma-number": "^2.1.0", "copy-to-clipboard": "^3.3.3", "flatpickr": "^4.6.13", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "^15.4.1", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-datepicker": "^8.4.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.60.0", "react-modern-drawer": "^1.4.0", "react-redux": "^9.2.0", "react-responsive": "^10.0.1", "react-toastify": "^11.0.5", "react-virtuoso": "^4.13.0", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@types/comma-number": "^2.1.2", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-transition-group": "^4.4.12", "eslint": "^9", "eslint-config-next": "15.1.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "tailwindcss": "^4.0.0", "typescript": "^5"}, "overrides": {"@react-jvectormap/core": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/world": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{css,scss,md,json}": ["prettier --write"]}}